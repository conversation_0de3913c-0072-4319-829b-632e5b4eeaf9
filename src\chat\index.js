const AWS = require('aws-sdk');
const { validateBody } = require('../shared/validation');
const { schemas } = require('../shared/validation');
const response = require('../shared/response');
const auth = require('../shared/auth');
const database = require('../shared/database');
const { v4: uuidv4 } = require('uuid');

/**
 * Chat Service - Handle WebSocket connections and real-time messaging
 */

const apiGateway = new AWS.ApiGatewayManagementApi({
  endpoint: process.env.WEBSOCKET_ENDPOINT
});

exports.handler = async (event, context) => {
  console.log('Chat service event:', JSON.stringify(event, null, 2));

  try {
    const { requestContext } = event;
    const { routeKey, connectionId } = requestContext;

    switch (routeKey) {
      case '$connect':
        return await handleConnect(event);
      
      case '$disconnect':
        return await handleDisconnect(event);
      
      case '$default':
        return await handleMessage(event);
      
      default:
        return { statusCode: 400, body: 'Unknown route' };
    }
  } catch (error) {
    console.error('Chat service error:', error);
    return { statusCode: 500, body: 'Internal server error' };
  }
};

/**
 * Handle WebSocket connection
 */
async function handleConnect(event) {
  try {
    const { connectionId } = event.requestContext;
    const { queryStringParameters } = event;

    // Extract token from query parameters
    const token = queryStringParameters?.token;
    if (!token) {
      return { statusCode: 401, body: 'Unauthorized: Token required' };
    }

    // Verify token and extract user
    const user = auth.extractUserFromToken({ headers: { Authorization: `Bearer ${token}` } });
    if (!user) {
      return { statusCode: 401, body: 'Unauthorized: Invalid token' };
    }

    // Get user's couple information
    const userProfile = await database.get(`USER#${user.userId}`, 'PROFILE');
    if (!userProfile || !userProfile.coupleId) {
      return { statusCode: 403, body: 'Forbidden: User not in a couple' };
    }

    // Store connection information
    const connection = {
      PK: `CONNECTION#${connectionId}`,
      SK: 'METADATA',
      connectionId,
      userId: user.userId,
      coupleId: userProfile.coupleId,
      connectedAt: new Date().toISOString(),
      GSI1PK: 'TYPE#CONNECTION',
      GSI1SK: `USER#${user.userId}`,
      TTL: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24 hours
    };

    await database.put(connection);

    // Notify partner that user is online
    await notifyPartnerStatus(userProfile.coupleId, user.userId, 'online');

    return { statusCode: 200, body: 'Connected' };
  } catch (error) {
    console.error('Connect error:', error);
    return { statusCode: 500, body: 'Connection failed' };
  }
}

/**
 * Handle WebSocket disconnection
 */
async function handleDisconnect(event) {
  try {
    const { connectionId } = event.requestContext;

    // Get connection info
    const connection = await database.get(`CONNECTION#${connectionId}`, 'METADATA');
    
    if (connection) {
      // Remove connection
      await database.delete(`CONNECTION#${connectionId}`, 'METADATA');

      // Notify partner that user is offline
      await notifyPartnerStatus(connection.coupleId, connection.userId, 'offline');
    }

    return { statusCode: 200, body: 'Disconnected' };
  } catch (error) {
    console.error('Disconnect error:', error);
    return { statusCode: 500, body: 'Disconnection failed' };
  }
}

/**
 * Handle incoming messages
 */
async function handleMessage(event) {
  try {
    const { connectionId } = event.requestContext;
    const body = JSON.parse(event.body || '{}');
    const { action, data } = body;

    // Get connection info
    const connection = await database.get(`CONNECTION#${connectionId}`, 'METADATA');
    if (!connection) {
      return { statusCode: 403, body: 'Connection not found' };
    }

    switch (action) {
      case 'sendMessage':
        return await handleSendMessage(connection, data);
      
      case 'markAsRead':
        return await handleMarkAsRead(connection, data);
      
      case 'getHistory':
        return await handleGetHistory(connection, data);
      
      case 'typing':
        return await handleTyping(connection, data);
      
      default:
        return { statusCode: 400, body: 'Unknown action' };
    }
  } catch (error) {
    console.error('Message handling error:', error);
    return { statusCode: 500, body: 'Message handling failed' };
  }
}

/**
 * Send a message
 */
async function handleSendMessage(connection, data) {
  // Validate message data
  const validation = validateBody(data, schemas.chat.sendMessage);
  if (!validation.isValid) {
    await sendToConnection(connection.connectionId, {
      type: 'error',
      message: 'Invalid message data',
      errors: validation.errors
    });
    return { statusCode: 400, body: 'Invalid message data' };
  }

  const { content, type, attachmentUrl } = validation.value;

  try {
    // Create message
    const messageId = uuidv4();
    const timestamp = Date.now();
    
    const message = {
      PK: `COUPLE#${connection.coupleId}`,
      SK: `MESSAGE#${timestamp}#${messageId}`,
      messageId,
      senderId: connection.userId,
      content,
      type,
      attachmentUrl,
      timestamp,
      status: 'sent',
      createdAt: new Date().toISOString(),
      GSI1PK: 'TYPE#MESSAGE',
      GSI1SK: `COUPLE#${connection.coupleId}#${timestamp}`
    };

    await database.put(message);

    // Get partner's connection
    const userProfile = await database.get(`USER#${connection.userId}`, 'PROFILE');
    const partnerId = userProfile.partnerId;
    
    const partnerConnections = await database.queryGSI1(
      'TYPE#CONNECTION',
      {
        expression: 'begins_with(GSI1SK, :sk)',
        values: { ':sk': `USER#${partnerId}` }
      }
    );

    // Send message to sender (confirmation)
    await sendToConnection(connection.connectionId, {
      type: 'messageConfirmation',
      message: {
        ...message,
        status: 'sent'
      }
    });

    // Send message to partner if online
    if (partnerConnections.items.length > 0) {
      for (const partnerConnection of partnerConnections.items) {
        await sendToConnection(partnerConnection.connectionId, {
          type: 'newMessage',
          message
        });
      }
      
      // Update message status to delivered
      await database.update(
        message.PK,
        message.SK,
        'SET #status = :status, deliveredAt = :deliveredAt',
        {
          ':status': 'delivered',
          ':deliveredAt': new Date().toISOString()
        },
        {
          '#status': 'status'
        }
      );
    }

    return { statusCode: 200, body: 'Message sent' };
  } catch (error) {
    console.error('Send message error:', error);
    await sendToConnection(connection.connectionId, {
      type: 'error',
      message: 'Failed to send message'
    });
    return { statusCode: 500, body: 'Failed to send message' };
  }
}

/**
 * Mark messages as read
 */
async function handleMarkAsRead(connection, data) {
  const { messageIds } = data;

  if (!messageIds || !Array.isArray(messageIds)) {
    return { statusCode: 400, body: 'Invalid message IDs' };
  }

  try {
    // Update messages as read
    for (const messageId of messageIds) {
      // Find the message
      const messages = await database.query(
        `COUPLE#${connection.coupleId}`,
        {
          expression: 'contains(SK, :messageId)',
          values: { ':messageId': messageId }
        }
      );

      if (messages.items.length > 0) {
        const message = messages.items[0];
        
        // Only mark as read if user is the recipient
        if (message.senderId !== connection.userId) {
          await database.update(
            message.PK,
            message.SK,
            'SET #status = :status, readAt = :readAt',
            {
              ':status': 'read',
              ':readAt': new Date().toISOString()
            },
            {
              '#status': 'status'
            }
          );
        }
      }
    }

    // Notify sender that messages were read
    const userProfile = await database.get(`USER#${connection.userId}`, 'PROFILE');
    const partnerId = userProfile.partnerId;
    
    const partnerConnections = await database.queryGSI1(
      'TYPE#CONNECTION',
      {
        expression: 'begins_with(GSI1SK, :sk)',
        values: { ':sk': `USER#${partnerId}` }
      }
    );

    for (const partnerConnection of partnerConnections.items) {
      await sendToConnection(partnerConnection.connectionId, {
        type: 'messagesRead',
        messageIds,
        readBy: connection.userId
      });
    }

    return { statusCode: 200, body: 'Messages marked as read' };
  } catch (error) {
    console.error('Mark as read error:', error);
    return { statusCode: 500, body: 'Failed to mark messages as read' };
  }
}

/**
 * Get chat history
 */
async function handleGetHistory(connection, data) {
  const { limit = 50, lastMessageTimestamp } = data;

  try {
    const queryOptions = {
      ScanIndexForward: false, // Most recent first
      Limit: limit
    };

    if (lastMessageTimestamp) {
      queryOptions.ExclusiveStartKey = {
        PK: `COUPLE#${connection.coupleId}`,
        SK: `MESSAGE#${lastMessageTimestamp}`
      };
    }

    const messages = await database.query(
      `COUPLE#${connection.coupleId}`,
      {
        expression: 'begins_with(SK, :sk)',
        values: { ':sk': 'MESSAGE#' }
      },
      queryOptions
    );

    await sendToConnection(connection.connectionId, {
      type: 'chatHistory',
      messages: messages.items.reverse(), // Reverse to show oldest first
      hasMore: !!messages.lastEvaluatedKey
    });

    return { statusCode: 200, body: 'History sent' };
  } catch (error) {
    console.error('Get history error:', error);
    await sendToConnection(connection.connectionId, {
      type: 'error',
      message: 'Failed to get chat history'
    });
    return { statusCode: 500, body: 'Failed to get history' };
  }
}

/**
 * Handle typing indicator
 */
async function handleTyping(connection, data) {
  const { isTyping } = data;

  try {
    // Get partner's connection
    const userProfile = await database.get(`USER#${connection.userId}`, 'PROFILE');
    const partnerId = userProfile.partnerId;
    
    const partnerConnections = await database.queryGSI1(
      'TYPE#CONNECTION',
      {
        expression: 'begins_with(GSI1SK, :sk)',
        values: { ':sk': `USER#${partnerId}` }
      }
    );

    // Send typing indicator to partner
    for (const partnerConnection of partnerConnections.items) {
      await sendToConnection(partnerConnection.connectionId, {
        type: 'typing',
        userId: connection.userId,
        isTyping
      });
    }

    return { statusCode: 200, body: 'Typing indicator sent' };
  } catch (error) {
    console.error('Typing indicator error:', error);
    return { statusCode: 500, body: 'Failed to send typing indicator' };
  }
}

/**
 * Send data to a WebSocket connection
 */
async function sendToConnection(connectionId, data) {
  try {
    await apiGateway.postToConnection({
      ConnectionId: connectionId,
      Data: JSON.stringify(data)
    }).promise();
  } catch (error) {
    if (error.statusCode === 410) {
      // Connection is stale, remove it
      await database.delete(`CONNECTION#${connectionId}`, 'METADATA');
    } else {
      console.error('Send to connection error:', error);
    }
  }
}

/**
 * Notify partner about online status
 */
async function notifyPartnerStatus(coupleId, userId, status) {
  try {
    // Get partner ID
    const userProfile = await database.get(`USER#${userId}`, 'PROFILE');
    if (!userProfile || !userProfile.partnerId) {
      return;
    }

    const partnerId = userProfile.partnerId;
    
    // Get partner's connections
    const partnerConnections = await database.queryGSI1(
      'TYPE#CONNECTION',
      {
        expression: 'begins_with(GSI1SK, :sk)',
        values: { ':sk': `USER#${partnerId}` }
      }
    );

    // Send status update to partner
    for (const connection of partnerConnections.items) {
      await sendToConnection(connection.connectionId, {
        type: 'partnerStatus',
        partnerId: userId,
        status,
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    console.error('Notify partner status error:', error);
  }
}
