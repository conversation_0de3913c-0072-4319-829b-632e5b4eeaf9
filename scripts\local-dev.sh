#!/bin/bash

# Local Development Setup Script for HopieApp
# Usage: ./scripts/local-dev.sh

set -e

echo "🛠️  Setting up HopieApp for local development..."

# Check prerequisites
echo "🔍 Checking prerequisites..."

# Check if Docker is running
if ! docker info &> /dev/null; then
    echo "❌ Error: Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if SAM CLI is installed
if ! command -v sam &> /dev/null; then
    echo "❌ Error: SAM CLI is not installed"
    echo "📖 Install SAM CLI: https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/serverless-sam-cli-install.html"
    exit 1
fi

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    echo "❌ Error: AWS CLI is not installed"
    echo "📖 Install AWS CLI: https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html"
    exit 1
fi

echo "✅ Prerequisites check passed"

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Build the application
echo "🔨 Building application..."
sam build --cached --parallel

# Start DynamoDB Local
echo "🗄️  Starting DynamoDB Local..."
docker run -d --name hopie-dynamodb-local -p 8000:8000 amazon/dynamodb-local:latest

# Wait for DynamoDB to be ready
echo "⏳ Waiting for DynamoDB Local to be ready..."
sleep 5

# Create local DynamoDB table
echo "📊 Creating local DynamoDB table..."
aws dynamodb create-table \
    --table-name HopieApp-local \
    --attribute-definitions \
        AttributeName=PK,AttributeType=S \
        AttributeName=SK,AttributeType=S \
        AttributeName=GSI1PK,AttributeType=S \
        AttributeName=GSI1SK,AttributeType=S \
        AttributeName=GSI2PK,AttributeType=S \
        AttributeName=GSI2SK,AttributeType=S \
    --key-schema \
        AttributeName=PK,KeyType=HASH \
        AttributeName=SK,KeyType=RANGE \
    --billing-mode PAY_PER_REQUEST \
    --global-secondary-indexes \
        "[
            {
                \"IndexName\": \"GSI1\",
                \"KeySchema\": [
                    {\"AttributeName\": \"GSI1PK\", \"KeyType\": \"HASH\"},
                    {\"AttributeName\": \"GSI1SK\", \"KeyType\": \"RANGE\"}
                ],
                \"Projection\": {
                    \"ProjectionType\": \"ALL\"
                }
            },
            {
                \"IndexName\": \"GSI2\",
                \"KeySchema\": [
                    {\"AttributeName\": \"GSI2PK\", \"KeyType\": \"HASH\"},
                    {\"AttributeName\": \"GSI2SK\", \"KeyType\": \"RANGE\"}
                ],
                \"Projection\": {
                    \"ProjectionType\": \"ALL\"
                }
            }
        ]" \
    --endpoint-url http://localhost:8000 \
    --region us-east-1

echo "✅ Local DynamoDB table created"

# Create local environment configuration
echo "📝 Creating local environment configuration..."
mkdir -p config
cat > config/local.json << EOF
{
  "environment": "local",
  "apiUrl": "http://localhost:3000",
  "webSocketUrl": "ws://localhost:3001",
  "cloudFrontUrl": "http://localhost:8080",
  "cognito": {
    "userPoolId": "local-user-pool",
    "userPoolClientId": "local-client-id",
    "region": "us-east-1"
  },
  "dynamodb": {
    "endpoint": "http://localhost:8000",
    "region": "us-east-1"
  },
  "s3": {
    "endpoint": "http://localhost:9000",
    "region": "us-east-1"
  }
}
EOF

# Create local environment variables
cat > .env.local << EOF
# Local Development Environment Variables
DYNAMODB_TABLE=HopieApp-local
DYNAMODB_ENDPOINT=http://localhost:8000
USER_POOL_ID=local-user-pool
USER_POOL_CLIENT_ID=local-client-id
USER_CONTENT_BUCKET=hopie-user-content-local
APP_ASSETS_BUCKET=hopie-app-assets-local
STAGE=local
AWS_REGION=us-east-1
EOF

echo "✅ Local environment configuration created"

# Seed local data
echo "🌱 Seeding local data..."
DYNAMODB_ENDPOINT=http://localhost:8000 node scripts/seed-local-data.js

echo "🎉 Local development setup completed!"
echo ""
echo "🚀 To start the local API server:"
echo "   sam local start-api --port 3000 --env-vars .env.local"
echo ""
echo "🔌 To start the local WebSocket server:"
echo "   sam local start-api --port 3001 --env-vars .env.local"
echo ""
echo "🗄️  DynamoDB Local is running on: http://localhost:8000"
echo "🌐 DynamoDB Admin UI: https://dynamodb-admin.com/"
echo ""
echo "🛑 To stop local services:"
echo "   docker stop hopie-dynamodb-local"
echo "   docker rm hopie-dynamodb-local"
