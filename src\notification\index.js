const AWS = require('aws-sdk');
const response = require('../shared/response');
const auth = require('../shared/auth');
const database = require('../shared/database');

/**
 * Notification Service - Handle push notifications and DynamoDB stream events
 */

exports.handler = async (event, context) => {
  console.log('Notification service event:', JSON.stringify(event, null, 2));

  try {
    // Check if this is a DynamoDB stream event
    if (event.Records && event.Records[0].eventSource === 'aws:dynamodb') {
      return await handleDynamoDBStream(event);
    }

    // Handle HTTP requests
    const { httpMethod, path, pathParameters } = event;
    const body = event.body ? JSON.parse(event.body) : {};

    // Handle CORS preflight
    if (httpMethod === 'OPTIONS') {
      return response.corsResponse();
    }

    // Extract user from token
    const user = auth.extractUserFromToken(event);
    if (!user) {
      return response.unauthorized();
    }

    // Route to appropriate handler
    const route = `${httpMethod} ${path}`;
    
    switch (route) {
      case 'GET /notifications':
        return await handleGetNotifications(user);
      
      case 'POST /notifications/register-device':
        return await handleRegisterDevice(user, body);
      
      case 'PUT /notifications/settings':
        return await handleUpdateSettings(user, body);
      
      case 'DELETE /notifications':
        return await handleDeleteNotification(user, pathParameters);
      
      default:
        return response.notFound('Endpoint not found');
    }
  } catch (error) {
    console.error('Notification service error:', error);
    return response.handleError(error);
  }
};

/**
 * Handle DynamoDB stream events
 */
async function handleDynamoDBStream(event) {
  try {
    for (const record of event.Records) {
      if (record.eventName === 'INSERT') {
        await processStreamRecord(record);
      }
    }

    return { statusCode: 200, body: 'Stream processed successfully' };
  } catch (error) {
    console.error('DynamoDB stream processing error:', error);
    return { statusCode: 500, body: 'Stream processing failed' };
  }
}

/**
 * Process individual stream record
 */
async function processStreamRecord(record) {
  try {
    const newImage = record.dynamodb.NewImage;
    const sk = newImage.SK?.S;

    // Handle different event types
    if (sk === 'TREE_STAGE_CHANGE') {
      await handleTreeStageChangeNotification(newImage);
    } else if (sk === 'COUPLE_INVITATION') {
      await handleCoupleInvitationNotification(newImage);
    } else if (sk === 'COUPLE_FORMED') {
      await handleCoupleFormedNotification(newImage);
    } else if (sk?.startsWith('MESSAGE#')) {
      await handleNewMessageNotification(newImage);
    }
  } catch (error) {
    console.error('Process stream record error:', error);
  }
}

/**
 * Handle tree stage change notification
 */
async function handleTreeStageChangeNotification(eventData) {
  try {
    const coupleId = eventData.coupleId?.S;
    const newStage = eventData.newStage?.S;
    const newLevel = eventData.newLevel?.N;

    if (!coupleId) return;

    // Get couple information
    const couple = await database.get(`COUPLE#${coupleId}`, 'METADATA');
    if (!couple) return;

    // Send notification to both users
    const notification = {
      title: '🌳 ¡Su árbol ha crecido!',
      body: `Su árbol ha alcanzado la etapa: ${newStage} (Nivel ${newLevel})`,
      type: 'TREE_GROWTH',
      data: {
        coupleId,
        newStage,
        newLevel
      }
    };

    await sendNotificationToUser(couple.user1Id, notification);
    await sendNotificationToUser(couple.user2Id, notification);

    console.log('Tree stage change notification sent');
  } catch (error) {
    console.error('Tree stage change notification error:', error);
  }
}

/**
 * Handle couple invitation notification
 */
async function handleCoupleInvitationNotification(eventData) {
  try {
    const toUserId = eventData.toUserId?.S;
    const fromUserName = eventData.fromUserName?.S;

    if (!toUserId || !fromUserName) return;

    const notification = {
      title: '💕 Nueva invitación de pareja',
      body: `${fromUserName} te ha invitado a formar una pareja`,
      type: 'COUPLE_INVITATION',
      data: {
        fromUserName
      }
    };

    await sendNotificationToUser(toUserId, notification);

    console.log('Couple invitation notification sent');
  } catch (error) {
    console.error('Couple invitation notification error:', error);
  }
}

/**
 * Handle couple formed notification
 */
async function handleCoupleFormedNotification(eventData) {
  try {
    const user1Id = eventData.user1Id?.S;
    const user2Id = eventData.user2Id?.S;

    if (!user1Id || !user2Id) return;

    const notification = {
      title: '🎉 ¡Pareja formada!',
      body: 'Su pareja ha sido formada exitosamente. ¡Comience su viaje juntos!',
      type: 'COUPLE_FORMED',
      data: {}
    };

    await sendNotificationToUser(user1Id, notification);
    await sendNotificationToUser(user2Id, notification);

    console.log('Couple formed notification sent');
  } catch (error) {
    console.error('Couple formed notification error:', error);
  }
}

/**
 * Handle new message notification
 */
async function handleNewMessageNotification(messageData) {
  try {
    const senderId = messageData.senderId?.S;
    const content = messageData.content?.S;
    const coupleId = messageData.PK?.S?.replace('COUPLE#', '');

    if (!senderId || !content || !coupleId) return;

    // Get couple information
    const couple = await database.get(`COUPLE#${coupleId}`, 'METADATA');
    if (!couple) return;

    // Get sender information
    const sender = await database.get(`USER#${senderId}`, 'PROFILE');
    if (!sender) return;

    // Determine recipient
    const recipientId = couple.user1Id === senderId ? couple.user2Id : couple.user1Id;

    const notification = {
      title: `💬 Mensaje de ${sender.name}`,
      body: content.length > 50 ? content.substring(0, 50) + '...' : content,
      type: 'NEW_MESSAGE',
      data: {
        senderId,
        senderName: sender.name,
        coupleId
      }
    };

    await sendNotificationToUser(recipientId, notification);

    console.log('New message notification sent');
  } catch (error) {
    console.error('New message notification error:', error);
  }
}

/**
 * Send notification to a specific user
 */
async function sendNotificationToUser(userId, notification) {
  try {
    // For now, just log the notification
    // In production, you would integrate with FCM, SNS, or another push notification service
    console.log(`Sending notification to user ${userId}:`, notification);

    // Store notification in database for in-app display
    const notificationRecord = {
      PK: `USER#${userId}`,
      SK: `NOTIFICATION#${Date.now()}`,
      userId,
      title: notification.title,
      body: notification.body,
      type: notification.type,
      data: notification.data,
      read: false,
      createdAt: new Date().toISOString(),
      GSI1PK: 'TYPE#NOTIFICATION',
      GSI1SK: `USER#${userId}#${Date.now()}`,
      TTL: Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60) // 30 days
    };

    await database.put(notificationRecord);
  } catch (error) {
    console.error('Send notification to user error:', error);
  }
}

/**
 * Get notifications for user
 */
async function handleGetNotifications(user) {
  try {
    const notifications = await database.query(
      `USER#${user.userId}`,
      {
        expression: 'begins_with(SK, :sk)',
        values: { ':sk': 'NOTIFICATION#' }
      },
      {
        ScanIndexForward: false, // Most recent first
        Limit: 50
      }
    );

    return response.success({
      notifications: notifications.items,
      unreadCount: notifications.items.filter(n => !n.read).length
    });
  } catch (error) {
    return response.handleError(error);
  }
}

/**
 * Register device for push notifications
 */
async function handleRegisterDevice(user, body) {
  // Implementation for registering device tokens
  return response.success({ message: 'Device registered successfully' });
}

/**
 * Update notification settings
 */
async function handleUpdateSettings(user, body) {
  // Implementation for updating notification settings
  return response.success({ message: 'Notification settings updated successfully' });
}

/**
 * Delete notification
 */
async function handleDeleteNotification(user, pathParameters) {
  // Implementation for deleting notifications
  return response.success({ message: 'Notification deleted successfully' });
}
