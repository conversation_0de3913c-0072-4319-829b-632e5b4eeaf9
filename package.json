{"name": "hopie-app-backend", "version": "1.0.0", "description": "<PERSON><PERSON><PERSON>pp - <PERSON><PERSON> Backend for Couples App", "main": "index.js", "scripts": {"build": "sam build", "deploy:dev": "sam deploy --config-env dev", "deploy:staging": "sam deploy --config-env staging", "deploy:prod": "sam deploy --config-env prod", "local": "sam local start-api --port 3000", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "format": "prettier --write src/", "validate": "sam validate", "logs:auth": "sam logs -n AuthFunction --stack-name hopie-app-dev --tail", "logs:tree": "sam logs -n TreeFunction --stack-name hopie-app-dev --tail", "logs:chat": "sam logs -n ChatFunction --stack-name hopie-app-dev --tail", "create-table": "aws dynamodb create-table --cli-input-json file://scripts/create-dynamodb-table.json", "seed-data": "node scripts/seed-data.js", "setup:dev": "npm run build && npm run deploy:dev && npm run seed-data"}, "keywords": ["serverless", "aws", "lambda", "dynamodb", "couples", "relationship", "app"], "author": "HopieApp Team", "license": "MIT", "dependencies": {"aws-sdk": "^2.1490.0", "uuid": "^9.0.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "moment": "^2.29.4", "sharp": "^0.32.6", "axios": "^1.6.0"}, "devDependencies": {"@types/aws-lambda": "^8.10.126", "@types/jest": "^29.5.8", "@types/node": "^20.9.0", "eslint": "^8.53.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-node": "^11.1.0", "jest": "^29.7.0", "prettier": "^3.0.3", "aws-sdk-mock": "^5.8.0"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js", "!src/**/*.test.js"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"]}, "engines": {"node": ">=18.0.0"}}