// Jest setup file for HopieApp tests

// Mock AWS SDK
jest.mock('aws-sdk', () => {
  const mockDynamoDB = {
    get: jest.fn().mockReturnValue({
      promise: jest.fn().mockResolvedValue({ Item: {} })
    }),
    put: jest.fn().mockReturnValue({
      promise: jest.fn().mockResolvedValue({})
    }),
    update: jest.fn().mockReturnValue({
      promise: jest.fn().mockResolvedValue({ Attributes: {} })
    }),
    delete: jest.fn().mockReturnValue({
      promise: jest.fn().mockResolvedValue({})
    }),
    query: jest.fn().mockReturnValue({
      promise: jest.fn().mockResolvedValue({ Items: [], Count: 0 })
    }),
    scan: jest.fn().mockReturnValue({
      promise: jest.fn().mockResolvedValue({ Items: [], Count: 0 })
    }),
    batchGet: jest.fn().mockReturnValue({
      promise: jest.fn().mockResolvedValue({ Responses: {} })
    }),
    batchWrite: jest.fn().mockReturnValue({
      promise: jest.fn().mockResolvedValue({})
    }),
    transactWrite: jest.fn().mockReturnValue({
      promise: jest.fn().mockResolvedValue({})
    }),
    transactGet: jest.fn().mockReturnValue({
      promise: jest.fn().mockResolvedValue({ Responses: [] })
    })
  };

  const mockCognito = {
    signUp: jest.fn().mockReturnValue({
      promise: jest.fn().mockResolvedValue({ UserSub: 'test-user-id' })
    }),
    confirmSignUp: jest.fn().mockReturnValue({
      promise: jest.fn().mockResolvedValue({})
    }),
    initiateAuth: jest.fn().mockReturnValue({
      promise: jest.fn().mockResolvedValue({
        AuthenticationResult: {
          AccessToken: 'mock-access-token',
          IdToken: 'mock-id-token',
          RefreshToken: 'mock-refresh-token',
          ExpiresIn: 3600
        }
      })
    }),
    getUser: jest.fn().mockReturnValue({
      promise: jest.fn().mockResolvedValue({
        Username: 'test-user',
        UserAttributes: [
          { Name: 'email', Value: '<EMAIL>' },
          { Name: 'name', Value: 'Test User' }
        ]
      })
    }),
    updateUserAttributes: jest.fn().mockReturnValue({
      promise: jest.fn().mockResolvedValue({})
    }),
    globalSignOut: jest.fn().mockReturnValue({
      promise: jest.fn().mockResolvedValue({})
    }),
    forgotPassword: jest.fn().mockReturnValue({
      promise: jest.fn().mockResolvedValue({})
    }),
    confirmForgotPassword: jest.fn().mockReturnValue({
      promise: jest.fn().mockResolvedValue({})
    })
  };

  const mockS3 = {
    getSignedUrl: jest.fn().mockReturnValue('https://mock-signed-url.com'),
    getObject: jest.fn().mockReturnValue({
      promise: jest.fn().mockResolvedValue({ Body: Buffer.from('mock-image-data') })
    }),
    putObject: jest.fn().mockReturnValue({
      promise: jest.fn().mockResolvedValue({})
    })
  };

  const mockApiGateway = {
    postToConnection: jest.fn().mockReturnValue({
      promise: jest.fn().mockResolvedValue({})
    })
  };

  return {
    DynamoDB: {
      DocumentClient: jest.fn(() => mockDynamoDB)
    },
    CognitoIdentityServiceProvider: jest.fn(() => mockCognito),
    S3: jest.fn(() => mockS3),
    ApiGatewayManagementApi: jest.fn(() => mockApiGateway),
    config: {
      update: jest.fn()
    }
  };
});

// Mock environment variables
process.env.DYNAMODB_TABLE = 'HopieApp-test';
process.env.USER_POOL_ID = 'us-east-2_test123456';
process.env.USER_POOL_CLIENT_ID = 'test-client-id';
process.env.USER_CONTENT_BUCKET = 'hopie-user-content-test';
process.env.APP_ASSETS_BUCKET = 'hopie-app-assets-test';
process.env.STAGE = 'test';
process.env.AWS_REGION = 'us-east-2';

// Global test utilities
global.mockEvent = (overrides = {}) => ({
  httpMethod: 'GET',
  path: '/test',
  pathParameters: {},
  queryStringParameters: {},
  headers: {
    'Authorization': 'Bearer mock-jwt-token'
  },
  body: null,
  requestContext: {
    authorizer: {
      claims: {
        sub: 'test-user-id',
        email: '<EMAIL>',
        name: 'Test User',
        email_verified: 'true'
      }
    }
  },
  ...overrides
});

global.mockContext = () => ({
  callbackWaitsForEmptyEventLoop: false,
  functionName: 'test-function',
  functionVersion: '$LATEST',
  invokedFunctionArn: 'arn:aws:lambda:us-east-2:123456789012:function:test-function',
  memoryLimitInMB: '512',
  awsRequestId: 'test-request-id',
  logGroupName: '/aws/lambda/test-function',
  logStreamName: '2024/01/01/[$LATEST]test-stream',
  getRemainingTimeInMillis: () => 30000
});

// Console override for cleaner test output
const originalConsole = console;
global.console = {
  ...originalConsole,
  log: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  info: jest.fn()
};
