const { validateBody } = require('../shared/validation');
const { schemas } = require('../shared/validation');
const response = require('../shared/response');
const auth = require('../shared/auth');
const database = require('../shared/database');
const { v4: uuidv4 } = require('uuid');

/**
 * Auth Service - Handle user authentication and registration
 */

exports.handler = async (event, context) => {
  console.log('Auth service event:', JSON.stringify(event, null, 2));

  try {
    const { httpMethod, path, pathParameters } = event;
    const body = event.body ? JSON.parse(event.body) : {};

    // Handle CORS preflight
    if (httpMethod === 'OPTIONS') {
      return response.corsResponse();
    }

    // Route to appropriate handler
    const route = `${httpMethod} ${path}`;
    
    switch (route) {
      case 'POST /auth/register':
        return await handleRegister(body);
      
      case 'POST /auth/login':
        return await handleLogin(body);
      
      case 'POST /auth/confirm':
        return await handleConfirm(body);
      
      case 'POST /auth/refresh':
        return await handleRefreshToken(body);
      
      case 'POST /auth/forgot-password':
        return await handleForgotPassword(body);
      
      case 'POST /auth/reset-password':
        return await handleResetPassword(body);
      
      case 'GET /auth/profile':
        return await handleGetProfile(event);
      
      case 'PUT /auth/profile':
        return await handleUpdateProfile(event, body);
      
      case 'POST /auth/logout':
        return await handleLogout(event);
      
      default:
        return response.notFound('Endpoint not found');
    }
  } catch (error) {
    console.error('Auth service error:', error);
    return response.handleError(error);
  }
};

/**
 * Register a new user
 */
async function handleRegister(body) {
  // Validate input
  const validation = validateBody(body, schemas.auth.register);
  if (!validation.isValid) {
    return response.validationError(validation.errors);
  }

  const { email, password, name } = validation.value;

  try {
    // Register with Cognito
    const cognitoResult = await auth.registerUser(email, password, name);
    
    // Create user record in DynamoDB
    const userId = cognitoResult.userId;
    const userRecord = {
      PK: `USER#${userId}`,
      SK: 'PROFILE',
      userId,
      email,
      name,
      status: 'pending_confirmation',
      settings: {
        notifications: true,
        locationSharing: false
      },
      GSI1PK: 'TYPE#USER',
      GSI1SK: `CREATED#${new Date().toISOString()}`
    };

    await database.put(userRecord);

    return response.created({
      userId,
      email,
      name,
      confirmationRequired: cognitoResult.confirmationRequired,
      message: 'User registered successfully. Please check your email for confirmation.'
    });
  } catch (error) {
    console.error('Registration error:', error);
    return response.handleError(error);
  }
}

/**
 * Login user
 */
async function handleLogin(body) {
  // Validate input
  const validation = validateBody(body, schemas.auth.login);
  if (!validation.isValid) {
    return response.validationError(validation.errors);
  }

  const { email, password } = validation.value;

  try {
    // Authenticate with Cognito
    const authResult = await auth.authenticateUser(email, password);
    
    if (authResult.challengeName) {
      return response.success({
        challengeName: authResult.challengeName,
        session: authResult.session,
        message: 'Authentication challenge required'
      });
    }

    // Update user's last login
    const userId = authResult.user.userId;
    await database.update(
      `USER#${userId}`,
      'PROFILE',
      'SET lastLoginAt = :lastLogin, #status = :status',
      {
        ':lastLogin': new Date().toISOString(),
        ':status': 'active'
      },
      {
        '#status': 'status'
      }
    );

    return response.success({
      tokens: {
        accessToken: authResult.accessToken,
        idToken: authResult.idToken,
        refreshToken: authResult.refreshToken,
        expiresIn: authResult.expiresIn
      },
      user: authResult.user
    });
  } catch (error) {
    console.error('Login error:', error);
    return response.handleError(error);
  }
}

/**
 * Confirm user registration
 */
async function handleConfirm(body) {
  const { email, confirmationCode } = body;

  if (!email || !confirmationCode) {
    return response.validationError([
      { field: 'email', message: 'Email is required' },
      { field: 'confirmationCode', message: 'Confirmation code is required' }
    ]);
  }

  try {
    await auth.confirmUser(email, confirmationCode);
    
    // Update user status in DynamoDB
    // First, find the user by email
    const users = await database.queryGSI1('TYPE#USER');
    const user = users.items.find(u => u.email === email);
    
    if (user) {
      await database.update(
        user.PK,
        user.SK,
        'SET #status = :status, emailVerified = :verified',
        {
          ':status': 'active',
          ':verified': true
        },
        {
          '#status': 'status'
        }
      );
    }

    return response.success({
      message: 'Email confirmed successfully. You can now log in.'
    });
  } catch (error) {
    console.error('Confirmation error:', error);
    return response.handleError(error);
  }
}

/**
 * Refresh access token
 */
async function handleRefreshToken(body) {
  const { refreshToken } = body;

  if (!refreshToken) {
    return response.validationError([
      { field: 'refreshToken', message: 'Refresh token is required' }
    ]);
  }

  try {
    const result = await auth.refreshToken(refreshToken);
    
    return response.success({
      accessToken: result.accessToken,
      idToken: result.idToken,
      expiresIn: result.expiresIn
    });
  } catch (error) {
    console.error('Token refresh error:', error);
    return response.handleError(error);
  }
}

/**
 * Initiate forgot password flow
 */
async function handleForgotPassword(body) {
  const { email } = body;

  if (!email) {
    return response.validationError([
      { field: 'email', message: 'Email is required' }
    ]);
  }

  try {
    await auth.forgotPassword(email);
    
    return response.success({
      message: 'Password reset code sent to your email.'
    });
  } catch (error) {
    console.error('Forgot password error:', error);
    return response.handleError(error);
  }
}

/**
 * Reset password with confirmation code
 */
async function handleResetPassword(body) {
  const { email, confirmationCode, newPassword } = body;

  if (!email || !confirmationCode || !newPassword) {
    return response.validationError([
      { field: 'email', message: 'Email is required' },
      { field: 'confirmationCode', message: 'Confirmation code is required' },
      { field: 'newPassword', message: 'New password is required' }
    ]);
  }

  try {
    await auth.confirmForgotPassword(email, confirmationCode, newPassword);
    
    return response.success({
      message: 'Password reset successfully. You can now log in with your new password.'
    });
  } catch (error) {
    console.error('Reset password error:', error);
    return response.handleError(error);
  }
}

/**
 * Get user profile
 */
async function handleGetProfile(event) {
  const user = auth.extractUserFromToken(event);
  if (!user) {
    return response.unauthorized();
  }

  try {
    // Get user profile from DynamoDB
    const userProfile = await database.get(`USER#${user.userId}`, 'PROFILE');
    
    if (!userProfile) {
      return response.notFound('User profile not found');
    }

    // Remove sensitive information
    const { PK, SK, GSI1PK, GSI1SK, ...profile } = userProfile;
    
    return response.success(profile);
  } catch (error) {
    console.error('Get profile error:', error);
    return response.handleError(error);
  }
}

/**
 * Update user profile
 */
async function handleUpdateProfile(event, body) {
  const user = auth.extractUserFromToken(event);
  if (!user) {
    return response.unauthorized();
  }

  // Validate input
  const validation = validateBody(body, schemas.auth.updateProfile);
  if (!validation.isValid) {
    return response.validationError(validation.errors);
  }

  try {
    const updates = validation.value;
    
    // Build update expression
    const updateExpressions = [];
    const expressionAttributeValues = {};
    const expressionAttributeNames = {};
    
    if (updates.name) {
      updateExpressions.push('#name = :name');
      expressionAttributeValues[':name'] = updates.name;
      expressionAttributeNames['#name'] = 'name';
    }
    
    if (updates.avatarUrl) {
      updateExpressions.push('avatarUrl = :avatarUrl');
      expressionAttributeValues[':avatarUrl'] = updates.avatarUrl;
    }
    
    if (updates.settings) {
      updateExpressions.push('settings = :settings');
      expressionAttributeValues[':settings'] = updates.settings;
    }

    if (updateExpressions.length === 0) {
      return response.validationError([
        { message: 'No valid fields to update' }
      ]);
    }

    const updateExpression = 'SET ' + updateExpressions.join(', ');
    
    const updatedProfile = await database.update(
      `USER#${user.userId}`,
      'PROFILE',
      updateExpression,
      expressionAttributeValues,
      expressionAttributeNames
    );

    // Remove DynamoDB keys from response
    const { PK, SK, GSI1PK, GSI1SK, ...profile } = updatedProfile;
    
    return response.success(profile);
  } catch (error) {
    console.error('Update profile error:', error);
    return response.handleError(error);
  }
}

/**
 * Logout user
 */
async function handleLogout(event) {
  const user = auth.extractUserFromToken(event);
  if (!user) {
    return response.unauthorized();
  }

  try {
    // Get access token from header
    const authHeader = event.headers?.Authorization || event.headers?.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const accessToken = authHeader.substring(7);
      await auth.signOut(accessToken);
    }

    return response.success({
      message: 'Logged out successfully'
    });
  } catch (error) {
    console.error('Logout error:', error);
    return response.handleError(error);
  }
}
