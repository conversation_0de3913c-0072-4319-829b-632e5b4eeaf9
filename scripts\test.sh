#!/bin/bash

# Test Script for HopieApp
# Usage: ./scripts/test.sh [unit|integration|e2e|all]

set -e

TEST_TYPE=${1:-all}

echo "🧪 Running HopieApp tests..."

# Check if Jest is available
if ! npm list jest &> /dev/null; then
    echo "❌ Error: Jest is not installed. Run 'npm install' first."
    exit 1
fi

case $TEST_TYPE in
    "unit")
        echo "🔬 Running unit tests..."
        npm run test -- --testPathPattern="\.unit\.test\.js$"
        ;;
    "integration")
        echo "🔗 Running integration tests..."
        npm run test -- --testPathPattern="\.integration\.test\.js$"
        ;;
    "e2e")
        echo "🌐 Running end-to-end tests..."
        npm run test -- --testPathPattern="\.e2e\.test\.js$"
        ;;
    "all")
        echo "🎯 Running all tests..."
        npm run test
        ;;
    *)
        echo "❌ Error: Invalid test type. Use: unit, integration, e2e, or all"
        exit 1
        ;;
esac

echo "✅ Tests completed successfully!"
