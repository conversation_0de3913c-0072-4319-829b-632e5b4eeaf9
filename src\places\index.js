const { validateBody } = require('../shared/validation');
const { schemas } = require('../shared/validation');
const response = require('../shared/response');
const auth = require('../shared/auth');
const database = require('../shared/database');
const { v4: uuidv4 } = require('uuid');

/**
 * Places Service - Handle special places management
 */

exports.handler = async (event, context) => {
  console.log('Places service event:', JSON.stringify(event, null, 2));

  try {
    const { httpMethod, path, pathParameters } = event;
    const body = event.body ? JSON.parse(event.body) : {};

    // Handle CORS preflight
    if (httpMethod === 'OPTIONS') {
      return response.corsResponse();
    }

    // Extract user from token
    const user = auth.extractUserFromToken(event);
    if (!user) {
      return response.unauthorized();
    }

    // Route to appropriate handler
    const route = `${httpMethod} ${path}`;
    
    switch (route) {
      case 'GET /places':
        return await handleGetPlaces(user);
      
      case 'POST /places':
        return await handleCreatePlace(user, body);
      
      case 'PUT /places':
        return await handleUpdatePlace(user, pathParameters, body);
      
      case 'DELETE /places':
        return await handleDeletePlace(user, pathParameters);
      
      default:
        return response.notFound('Endpoint not found');
    }
  } catch (error) {
    console.error('Places service error:', error);
    return response.handleError(error);
  }
};

async function handleGetPlaces(user) {
  try {
    const userProfile = await database.get(`USER#${user.userId}`, 'PROFILE');
    if (!userProfile || !userProfile.coupleId) {
      return response.notFound('User is not part of a couple');
    }

    const places = await database.query(
      `COUPLE#${userProfile.coupleId}`,
      {
        expression: 'begins_with(SK, :sk)',
        values: { ':sk': 'PLACE#' }
      }
    );

    return response.success({
      places: places.items
    });
  } catch (error) {
    return response.handleError(error);
  }
}

async function handleCreatePlace(user, body) {
  const validation = validateBody(body, schemas.place.create);
  if (!validation.isValid) {
    return response.validationError(validation.errors);
  }

  try {
    const userProfile = await database.get(`USER#${user.userId}`, 'PROFILE');
    if (!userProfile || !userProfile.coupleId) {
      return response.notFound('User is not part of a couple');
    }

    const placeId = uuidv4();
    const place = {
      PK: `COUPLE#${userProfile.coupleId}`,
      SK: `PLACE#${placeId}`,
      placeId,
      ...validation.value,
      createdBy: user.userId,
      GSI1PK: 'TYPE#PLACE',
      GSI1SK: `COUPLE#${userProfile.coupleId}#${placeId}`
    };

    await database.put(place);
    return response.created(place);
  } catch (error) {
    return response.handleError(error);
  }
}

async function handleUpdatePlace(user, pathParameters, body) {
  // Implementation for updating place
  return response.success({ message: 'Place updated successfully' });
}

async function handleDeletePlace(user, pathParameters) {
  // Implementation for deleting place
  return response.success({ message: 'Place deleted successfully' });
}
