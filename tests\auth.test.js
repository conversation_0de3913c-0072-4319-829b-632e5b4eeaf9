const { handler } = require('../src/auth/index');

describe('Auth Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('POST /auth/register', () => {
    it('should register a new user successfully', async () => {
      const event = mockEvent({
        httpMethod: 'POST',
        path: '/auth/register',
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'SecurePass123',
          name: 'New User',
          confirmPassword: 'SecurePass123'
        }),
        requestContext: {} // No auth required for registration
      });

      const result = await handler(event, mockContext());

      expect(result.statusCode).toBe(201);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(true);
      expect(body.data.email).toBe('<EMAIL>');
      expect(body.data.name).toBe('New User');
    });

    it('should return validation error for invalid input', async () => {
      const event = mockEvent({
        httpMethod: 'POST',
        path: '/auth/register',
        body: JSON.stringify({
          email: 'invalid-email',
          password: '123', // Too short
          name: 'A' // Too short
        }),
        requestContext: {}
      });

      const result = await handler(event, mockContext());

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.code).toBe('VALIDATION_ERROR');
    });
  });

  describe('POST /auth/login', () => {
    it('should login user successfully', async () => {
      const event = mockEvent({
        httpMethod: 'POST',
        path: '/auth/login',
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'SecurePass123'
        }),
        requestContext: {}
      });

      const result = await handler(event, mockContext());

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(true);
      expect(body.data.tokens).toBeDefined();
      expect(body.data.user).toBeDefined();
    });

    it('should return validation error for missing credentials', async () => {
      const event = mockEvent({
        httpMethod: 'POST',
        path: '/auth/login',
        body: JSON.stringify({}),
        requestContext: {}
      });

      const result = await handler(event, mockContext());

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
    });
  });

  describe('GET /auth/profile', () => {
    it('should return user profile', async () => {
      const event = mockEvent({
        httpMethod: 'GET',
        path: '/auth/profile'
      });

      const result = await handler(event, mockContext());

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(true);
      expect(body.data.userId).toBeDefined();
    });

    it('should return unauthorized for missing token', async () => {
      const event = mockEvent({
        httpMethod: 'GET',
        path: '/auth/profile',
        headers: {}, // No authorization header
        requestContext: {} // No authorizer claims
      });

      const result = await handler(event, mockContext());

      expect(result.statusCode).toBe(401);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
    });
  });

  describe('OPTIONS requests', () => {
    it('should handle CORS preflight requests', async () => {
      const event = mockEvent({
        httpMethod: 'OPTIONS',
        path: '/auth/register'
      });

      const result = await handler(event, mockContext());

      expect(result.statusCode).toBe(200);
      expect(result.headers['Access-Control-Allow-Origin']).toBe('*');
      expect(result.headers['Access-Control-Allow-Methods']).toBeDefined();
    });
  });

  describe('Unknown routes', () => {
    it('should return 404 for unknown routes', async () => {
      const event = mockEvent({
        httpMethod: 'GET',
        path: '/auth/unknown'
      });

      const result = await handler(event, mockContext());

      expect(result.statusCode).toBe(404);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.message).toBe('Endpoint not found');
    });
  });
});
