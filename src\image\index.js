const AWS = require('aws-sdk');
const response = require('../shared/response');
const auth = require('../shared/auth');
const database = require('../shared/database');
const { v4: uuidv4 } = require('uuid');

/**
 * Image Service - Handle image uploads and management
 */

const s3 = new AWS.S3();

exports.handler = async (event, context) => {
  console.log('Image service event:', JSON.stringify(event, null, 2));

  try {
    const { httpMethod, path, pathParameters } = event;
    const body = event.body ? JSON.parse(event.body) : {};

    // Handle CORS preflight
    if (httpMethod === 'OPTIONS') {
      return response.corsResponse();
    }

    // Extract user from token
    const user = auth.extractUserFromToken(event);
    if (!user) {
      return response.unauthorized();
    }

    // Route to appropriate handler
    const route = `${httpMethod} ${path}`;
    
    switch (route) {
      case 'POST /images/upload-url':
        return await handleGetUploadUrl(user, body);
      
      case 'POST /images/process':
        return await handleProcessImage(user, body);
      
      case 'DELETE /images':
        return await handleDeleteImage(user, pathParameters);
      
      case 'GET /images/signed-url':
        return await handleGetSignedUrl(user, pathParameters);
      
      default:
        return response.notFound('Endpoint not found');
    }
  } catch (error) {
    console.error('Image service error:', error);
    return response.handleError(error);
  }
};

async function handleGetUploadUrl(user, body) {
  const { fileName, fileType, category, entityId } = body;

  if (!fileName || !fileType || !category) {
    return response.validationError([
      { field: 'fileName', message: 'File name is required' },
      { field: 'fileType', message: 'File type is required' },
      { field: 'category', message: 'Category is required' }
    ]);
  }

  try {
    const userProfile = await database.get(`USER#${user.userId}`, 'PROFILE');
    if (!userProfile) {
      return response.notFound('User profile not found');
    }

    // Generate unique file path
    const fileExtension = fileName.split('.').pop();
    const uniqueFileName = `${uuidv4()}.${fileExtension}`;
    
    let filePath;
    switch (category) {
      case 'avatar':
        filePath = `avatars/user-${user.userId}/${uniqueFileName}`;
        break;
      case 'place':
        filePath = `couples/${userProfile.coupleId}/places/${entityId}/${uniqueFileName}`;
        break;
      case 'lifeplan':
        filePath = `couples/${userProfile.coupleId}/lifeplan/${entityId}/${uniqueFileName}`;
        break;
      case 'chat':
        filePath = `couples/${userProfile.coupleId}/chat/attachments/${uniqueFileName}`;
        break;
      default:
        return response.validationError([
          { field: 'category', message: 'Invalid category' }
        ]);
    }

    // Generate presigned URL
    const uploadUrl = s3.getSignedUrl('putObject', {
      Bucket: process.env.USER_CONTENT_BUCKET,
      Key: filePath,
      ContentType: fileType,
      Expires: 300 // 5 minutes
    });

    return response.success({
      uploadUrl,
      filePath,
      expiresIn: 300
    });
  } catch (error) {
    return response.handleError(error);
  }
}

async function handleProcessImage(user, body) {
  // Implementation for processing uploaded images
  return response.success({ message: 'Image processed successfully' });
}

async function handleDeleteImage(user, pathParameters) {
  // Implementation for deleting images
  return response.success({ message: 'Image deleted successfully' });
}

async function handleGetSignedUrl(user, pathParameters) {
  // Implementation for getting signed URLs for image access
  return response.success({ signedUrl: '' });
}
