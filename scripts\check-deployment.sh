#!/bin/bash

# Check Deployment Script for HopieApp
# Usage: ./scripts/check-deployment.sh [dev|staging|prod]

set -e

ENVIRONMENT=${1:-dev}
STACK_NAME="hopie-app-$ENVIRONMENT"

echo "🔍 Checking HopieApp $ENVIRONMENT deployment..."

# Check if stack exists
if ! aws cloudformation describe-stacks --stack-name $STACK_NAME &> /dev/null; then
    echo "❌ Stack $STACK_NAME does not exist"
    echo "Run: ./scripts/deploy.sh $ENVIRONMENT"
    exit 1
fi

echo "✅ Stack exists"

# Get stack outputs
echo "📋 Getting stack information..."

API_URL=$(aws cloudformation describe-stacks \
  --stack-name $STACK_NAME \
  --query 'Stacks[0].Outputs[?OutputKey==`ApiGatewayUrl`].OutputValue' \
  --output text)

WEBSOCKET_URL=$(aws cloudformation describe-stacks \
  --stack-name $STACK_NAME \
  --query 'Stacks[0].Outputs[?OutputKey==`WebSocketUrl`].OutputValue' \
  --output text)

CLOUDFRONT_URL=$(aws cloudformation describe-stacks \
  --stack-name $STACK_NAME \
  --query 'Stacks[0].Outputs[?OutputKey==`CloudFrontUrl`].OutputValue' \
  --output text)

USER_POOL_ID=$(aws cloudformation describe-stacks \
  --stack-name $STACK_NAME \
  --query 'Stacks[0].Outputs[?OutputKey==`UserPoolId`].OutputValue' \
  --output text)

DYNAMODB_TABLE=$(aws cloudformation describe-stacks \
  --stack-name $STACK_NAME \
  --query 'Stacks[0].Outputs[?OutputKey==`DynamoDBTable`].OutputValue' \
  --output text)

echo "🌐 API Gateway URL: $API_URL"
echo "🔌 WebSocket URL: $WEBSOCKET_URL"
echo "📦 CloudFront URL: $CLOUDFRONT_URL"
echo "👤 User Pool ID: $USER_POOL_ID"
echo "🗄️  DynamoDB Table: $DYNAMODB_TABLE"

# Test API endpoints
echo ""
echo "🧪 Testing API endpoints..."

# Test tree types endpoint (no auth required)
echo "  Testing GET /trees/types..."
if curl -s -f "$API_URL/trees/types" > /dev/null; then
    echo "  ✅ Tree types endpoint working"
else
    echo "  ❌ Tree types endpoint failed"
fi

# Test CORS
echo "  Testing CORS..."
if curl -s -f -X OPTIONS "$API_URL/trees/types" \
  -H "Origin: https://example.com" \
  -H "Access-Control-Request-Method: GET" > /dev/null; then
    echo "  ✅ CORS working"
else
    echo "  ❌ CORS failed"
fi

# Check DynamoDB table
echo ""
echo "🗄️  Checking DynamoDB table..."
if aws dynamodb describe-table --table-name $DYNAMODB_TABLE > /dev/null; then
    echo "  ✅ DynamoDB table accessible"
    
    # Check if table has data
    ITEM_COUNT=$(aws dynamodb scan --table-name $DYNAMODB_TABLE --select COUNT --query 'Count' --output text)
    echo "  📊 Items in table: $ITEM_COUNT"
else
    echo "  ❌ DynamoDB table not accessible"
fi

# Check Cognito User Pool
echo ""
echo "👤 Checking Cognito User Pool..."
if aws cognito-idp describe-user-pool --user-pool-id $USER_POOL_ID > /dev/null; then
    echo "  ✅ Cognito User Pool accessible"
else
    echo "  ❌ Cognito User Pool not accessible"
fi

# Check Lambda functions
echo ""
echo "⚡ Checking Lambda functions..."
FUNCTIONS=(
    "hopie-auth-$ENVIRONMENT"
    "hopie-tree-$ENVIRONMENT"
    "hopie-couple-$ENVIRONMENT"
    "hopie-questions-$ENVIRONMENT"
    "hopie-chat-$ENVIRONMENT"
)

for func in "${FUNCTIONS[@]}"; do
    if aws lambda get-function --function-name $func > /dev/null 2>&1; then
        echo "  ✅ $func"
    else
        echo "  ❌ $func not found"
    fi
done

# Check S3 buckets
echo ""
echo "📦 Checking S3 buckets..."
USER_CONTENT_BUCKET=$(aws cloudformation describe-stack-resources \
    --stack-name $STACK_NAME \
    --logical-resource-id UserContentBucket \
    --query 'StackResources[0].PhysicalResourceId' \
    --output text 2>/dev/null || echo "")

APP_ASSETS_BUCKET=$(aws cloudformation describe-stack-resources \
    --stack-name $STACK_NAME \
    --logical-resource-id AppAssetsBucket \
    --query 'StackResources[0].PhysicalResourceId' \
    --output text 2>/dev/null || echo "")

if [ ! -z "$USER_CONTENT_BUCKET" ] && aws s3 ls s3://$USER_CONTENT_BUCKET > /dev/null 2>&1; then
    echo "  ✅ User content bucket: $USER_CONTENT_BUCKET"
else
    echo "  ❌ User content bucket not accessible"
fi

if [ ! -z "$APP_ASSETS_BUCKET" ] && aws s3 ls s3://$APP_ASSETS_BUCKET > /dev/null 2>&1; then
    echo "  ✅ App assets bucket: $APP_ASSETS_BUCKET"
else
    echo "  ❌ App assets bucket not accessible"
fi

# Test CloudFront
echo ""
echo "🌐 Testing CloudFront distribution..."
if curl -s -f -I "$CLOUDFRONT_URL" > /dev/null; then
    echo "  ✅ CloudFront distribution working"
else
    echo "  ❌ CloudFront distribution not accessible"
fi

# Summary
echo ""
echo "📊 Deployment Check Summary"
echo "=========================="
echo "Environment: $ENVIRONMENT"
echo "Stack: $STACK_NAME"
echo "Region: us-east-2"
echo ""
echo "🔗 Important URLs:"
echo "  API: $API_URL"
echo "  WebSocket: $WEBSOCKET_URL"
echo "  CDN: $CLOUDFRONT_URL"
echo ""
echo "📝 Configuration file: config/$ENVIRONMENT.json"
echo ""
echo "🚀 Next steps:"
echo "  1. Test user registration: curl -X POST $API_URL/auth/register"
echo "  2. Configure your frontend with the URLs above"
echo "  3. Monitor logs: sam logs -n AuthFunction --stack-name $STACK_NAME --tail"
echo ""
echo "✅ Deployment check completed!"
