# 🚀 HopieApp - Guía de Despliegue Completa

Esta guía te llevará paso a paso para desplegar HopieApp en AWS usando la región **us-east-2** (Ohio).

## 📋 Prerrequisitos

### 1. Herramientas Requeridas

Instala las siguientes herramientas en tu sistema:

#### AWS CLI
```bash
# Windows (usando Chocolatey)
choco install awscli

# macOS (usando Homebrew)
brew install awscli

# Linux (usando pip)
pip install awscli

# Verificar instalación
aws --version
```

#### SAM CLI
```bash
# Windows (usando Chocolatey)
choco install aws-sam-cli

# macOS (usando Homebrew)
brew install aws-sam-cli

# Linux (usando pip)
pip install aws-sam-cli

# Verificar instalación
sam --version
```

#### Node.js (versión 18 o superior)
```bash
# Descargar desde: https://nodejs.org/
# Verificar instalación
node --version
npm --version
```

### 2. Configurar AWS CLI

```bash
# Configurar credenciales de AWS
aws configure

# Ingresa cuando se solicite:
# AWS Access Key ID: [Tu Access Key]
# AWS Secret Access Key: [Tu Secret Key]
# Default region name: us-east-2
# Default output format: json

# Verificar configuración
aws sts get-caller-identity
```

### 3. Permisos de IAM Requeridos

Tu usuario de AWS necesita los siguientes permisos:
- CloudFormation (crear, actualizar, eliminar stacks)
- Lambda (crear, actualizar funciones)
- DynamoDB (crear, gestionar tablas)
- S3 (crear, gestionar buckets)
- API Gateway (crear, gestionar APIs)
- Cognito (crear, gestionar User Pools)
- CloudFront (crear, gestionar distribuciones)
- IAM (crear roles para Lambda)

## 🛠️ Preparación del Proyecto

### 1. Clonar y Configurar

```bash
# Si tienes el código en un repositorio
git clone <tu-repositorio>
cd hopie-app-backend

# O si ya tienes los archivos localmente
cd bakApp

# Instalar dependencias
npm install
```

### 2. Validar el Proyecto

```bash
# Hacer ejecutables los scripts
chmod +x scripts/*.sh

# Ejecutar validación completa
./scripts/validate.sh
```

Si la validación falla, corrige los errores antes de continuar.

## 🚀 Despliegue Paso a Paso

### Paso 1: Despliegue a Desarrollo

```bash
# Desplegar a ambiente de desarrollo
./scripts/deploy.sh dev
```

Este comando:
1. Instala dependencias
2. Valida el template SAM
3. Construye la aplicación
4. Despliega a AWS
5. Crea datos iniciales
6. Genera archivo de configuración

### Paso 2: Verificar el Despliegue

Después del despliegue exitoso, verás algo como:

```
✅ Deployment completed successfully!

🌐 API Gateway URL: https://abc123.execute-api.us-east-2.amazonaws.com/dev
🔌 WebSocket URL: wss://def456.execute-api.us-east-2.amazonaws.com/dev
📦 CloudFront URL: https://ghi789.cloudfront.net
👤 User Pool ID: us-east-2_xyz123
🔑 User Pool Client ID: abcdef123456789
```

### Paso 3: Probar la API

```bash
# Obtener la URL de la API
API_URL=$(aws cloudformation describe-stacks \
  --stack-name hopie-app-dev \
  --query 'Stacks[0].Outputs[?OutputKey==`ApiGatewayUrl`].OutputValue' \
  --output text)

# Probar endpoint de tipos de árboles (no requiere autenticación)
curl $API_URL/trees/types
```

### Paso 4: Despliegue a Producción (Opcional)

```bash
# Desplegar a producción (requiere confirmación)
./scripts/deploy.sh prod
```

## 🔧 Configuración Post-Despliegue

### 1. Configurar Dominio Personalizado (Opcional)

Si tienes un dominio personalizado:

```bash
# Crear certificado SSL en ACM (debe ser en us-east-1 para CloudFront)
aws acm request-certificate \
  --domain-name api.tudominio.com \
  --validation-method DNS \
  --region us-east-1

# Configurar dominio personalizado en API Gateway
aws apigateway create-domain-name \
  --domain-name api.tudominio.com \
  --certificate-arn arn:aws:acm:us-east-1:123456789012:certificate/abc123
```

### 2. Configurar Monitoreo

```bash
# Crear alarmas de CloudWatch
aws cloudwatch put-metric-alarm \
  --alarm-name "HopieApp-HighErrorRate" \
  --alarm-description "High error rate in Lambda functions" \
  --metric-name Errors \
  --namespace AWS/Lambda \
  --statistic Sum \
  --period 300 \
  --threshold 10 \
  --comparison-operator GreaterThanThreshold
```

## 📱 Configuración del Frontend

Usa la configuración generada en `config/dev.json`:

```javascript
// Configuración para tu app frontend
const config = {
  apiUrl: "https://abc123.execute-api.us-east-2.amazonaws.com/dev",
  webSocketUrl: "wss://def456.execute-api.us-east-2.amazonaws.com/dev",
  cloudFrontUrl: "https://ghi789.cloudfront.net",
  cognito: {
    userPoolId: "us-east-2_xyz123",
    userPoolClientId: "abcdef123456789",
    region: "us-east-2"
  }
};
```

## 🧪 Pruebas de la API

### 1. Registro de Usuario

```bash
curl -X POST $API_URL/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "TestPass123",
    "name": "Test User",
    "confirmPassword": "TestPass123"
  }'
```

### 2. Login de Usuario

```bash
curl -X POST $API_URL/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "TestPass123"
  }'
```

### 3. Obtener Tipos de Árboles

```bash
curl $API_URL/trees/types
```

## 🔍 Monitoreo y Logs

### Ver Logs de Lambda

```bash
# Ver logs en tiempo real
sam logs -n AuthFunction --stack-name hopie-app-dev --tail

# Ver logs de un período específico
sam logs -n TreeFunction --stack-name hopie-app-dev \
  --start-time '2024-01-15T10:00:00' \
  --end-time '2024-01-15T11:00:00'
```

### Monitorear DynamoDB

```bash
# Ver métricas de DynamoDB
aws cloudwatch get-metric-statistics \
  --namespace AWS/DynamoDB \
  --metric-name ConsumedReadCapacityUnits \
  --dimensions Name=TableName,Value=HopieApp-dev \
  --start-time 2024-01-15T00:00:00Z \
  --end-time 2024-01-15T23:59:59Z \
  --period 3600 \
  --statistics Sum
```

## 🛠️ Solución de Problemas

### Error: "Stack already exists"

```bash
# Si el stack ya existe y falló, eliminarlo
aws cloudformation delete-stack --stack-name hopie-app-dev
aws cloudformation wait stack-delete-complete --stack-name hopie-app-dev

# Luego volver a desplegar
./scripts/deploy.sh dev
```

### Error: "Bucket already exists"

Los nombres de buckets S3 son únicos globalmente. El template ya incluye el ID de cuenta para evitar conflictos.

### Error: "Cognito domain already exists"

```bash
# Cambiar el prefijo del dominio
sam deploy --parameter-overrides CognitoDomainPrefix=hopie-app-dev-v2
```

### Error de permisos

```bash
# Verificar permisos del usuario
aws iam get-user
aws iam list-attached-user-policies --user-name tu-usuario
```

## 🧹 Limpieza

Para eliminar todos los recursos:

```bash
# Eliminar ambiente de desarrollo
./scripts/cleanup.sh dev

# Eliminar ambiente de producción (requiere confirmación)
./scripts/cleanup.sh prod
```

## 📊 Costos Estimados

### Ambiente de Desarrollo (uso ligero)
- Lambda: ~$1-5/mes
- DynamoDB: ~$1-3/mes
- S3: ~$1-2/mes
- CloudFront: ~$1/mes
- API Gateway: ~$1-3/mes
- Cognito: Gratis hasta 50,000 usuarios
- **Total estimado: $5-15/mes**

### Ambiente de Producción (uso moderado)
- Lambda: ~$10-50/mes
- DynamoDB: ~$10-30/mes
- S3: ~$5-15/mes
- CloudFront: ~$5-20/mes
- API Gateway: ~$10-30/mes
- Cognito: ~$5-15/mes
- **Total estimado: $45-160/mes**

## 🎯 Próximos Pasos

1. **Desarrollar Frontend**: Usa las URLs generadas para conectar tu app móvil/web
2. **Configurar CI/CD**: Implementa GitHub Actions para despliegue automático
3. **Añadir Monitoreo**: Configura alertas y dashboards en CloudWatch
4. **Optimizar Costos**: Revisa y ajusta la configuración según el uso real
5. **Seguridad**: Implementa WAF, rate limiting y otras medidas de seguridad

## 📞 Soporte

Si encuentras problemas:

1. Revisa los logs de CloudFormation en la consola de AWS
2. Verifica los logs de Lambda usando `sam logs`
3. Consulta la documentación de AWS SAM
4. Revisa los issues en el repositorio del proyecto

¡Tu aplicación HopieApp está lista para ayudar a las parejas a crecer juntas! 🌳💕
