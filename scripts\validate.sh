#!/bin/bash

# Validation Script for HopieApp
# Usage: ./scripts/validate.sh

set -e

echo "🔍 Validating HopieApp project..."

# Check if required files exist
echo "📁 Checking required files..."

REQUIRED_FILES=(
    "template.yaml"
    "samconfig.toml"
    "package.json"
    "src/shared/database.js"
    "src/shared/response.js"
    "src/shared/auth.js"
    "src/shared/validation.js"
    "src/auth/index.js"
    "src/tree/index.js"
    "src/couple/index.js"
    "src/questions/index.js"
    "src/chat/index.js"
)

for file in "${REQUIRED_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        echo "❌ Missing required file: $file"
        exit 1
    fi
done

echo "✅ All required files present"

# Validate SAM template
echo "🔍 Validating SAM template..."
if ! sam validate; then
    echo "❌ SAM template validation failed"
    exit 1
fi
echo "✅ SAM template is valid"

# Check Node.js dependencies
echo "📦 Checking Node.js dependencies..."
if [ ! -d "node_modules" ]; then
    echo "⚠️  Node modules not installed. Installing..."
    npm install
fi

# Lint code
echo "🔍 Linting code..."
if ! npm run lint; then
    echo "❌ Code linting failed"
    exit 1
fi
echo "✅ Code linting passed"

# Run tests
echo "🧪 Running tests..."
if ! npm test; then
    echo "❌ Tests failed"
    exit 1
fi
echo "✅ All tests passed"

# Check AWS CLI configuration
echo "🔍 Checking AWS CLI configuration..."
if ! aws sts get-caller-identity &> /dev/null; then
    echo "❌ AWS CLI not configured or credentials invalid"
    echo "Please run: aws configure"
    exit 1
fi
echo "✅ AWS CLI configured"

# Validate environment configurations
echo "🔍 Validating environment configurations..."
ENVIRONMENTS=("dev" "staging" "prod")

for env in "${ENVIRONMENTS[@]}"; do
    echo "  Checking $env environment..."
    
    # Check if samconfig has the environment
    if ! grep -q "\[$env\]" samconfig.toml; then
        echo "❌ Missing $env configuration in samconfig.toml"
        exit 1
    fi
    
    # Check region is us-east-2
    if ! grep -A 10 "\[$env\.deploy\.parameters\]" samconfig.toml | grep -q 'region = "us-east-2"'; then
        echo "❌ $env environment not configured for us-east-2 region"
        exit 1
    fi
done

echo "✅ All environment configurations valid"

# Check for security issues
echo "🔒 Checking for security issues..."

# Check for hardcoded secrets
if grep -r -i "password\|secret\|key" src/ --include="*.js" | grep -v "process.env" | grep -v "// " | grep -v "/\*" | grep "=" ; then
    echo "⚠️  Potential hardcoded secrets found. Please review:"
    grep -r -i "password\|secret\|key" src/ --include="*.js" | grep -v "process.env" | grep -v "// " | grep -v "/\*" | grep "="
    echo "Make sure all secrets use environment variables"
fi

# Check for console.log in production code (warnings only)
if grep -r "console.log" src/ --include="*.js" | grep -v "console.error" | grep -v "console.warn"; then
    echo "⚠️  console.log statements found in code. Consider using proper logging:"
    grep -r "console.log" src/ --include="*.js" | grep -v "console.error" | grep -v "console.warn"
fi

echo "✅ Security check completed"

# Validate package.json scripts
echo "🔍 Validating package.json scripts..."
REQUIRED_SCRIPTS=("build" "deploy:dev" "test" "lint")

for script in "${REQUIRED_SCRIPTS[@]}"; do
    if ! npm run $script --silent &> /dev/null; then
        echo "❌ npm script '$script' not working"
        exit 1
    fi
done

echo "✅ All npm scripts working"

# Check file permissions for scripts
echo "🔍 Checking script permissions..."
SCRIPT_FILES=(
    "scripts/deploy.sh"
    "scripts/cleanup.sh"
    "scripts/local-dev.sh"
    "scripts/test.sh"
)

for script in "${SCRIPT_FILES[@]}"; do
    if [ ! -x "$script" ]; then
        echo "⚠️  Making $script executable..."
        chmod +x "$script"
    fi
done

echo "✅ Script permissions correct"

# Final summary
echo ""
echo "🎉 Validation completed successfully!"
echo ""
echo "✅ Project is ready for deployment"
echo ""
echo "Next steps:"
echo "  1. Deploy to development: ./scripts/deploy.sh dev"
echo "  2. Test the deployment"
echo "  3. Deploy to production: ./scripts/deploy.sh prod"
echo ""
echo "For local development:"
echo "  1. Set up local environment: ./scripts/local-dev.sh"
echo "  2. Start local API: sam local start-api --port 3000"
