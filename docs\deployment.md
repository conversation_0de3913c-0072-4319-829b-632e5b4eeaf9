# 🚀 HopieApp Deployment Guide

Complete guide for deploying HopieApp to AWS using SAM (Serverless Application Model).

## 📋 Prerequisites

### Required Tools
- [AWS CLI](https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html) v2.0+
- [SAM CLI](https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/serverless-sam-cli-install.html) v1.50+
- [Node.js](https://nodejs.org/) v18+
- [Docker](https://www.docker.com/) (for local development)

### AWS Account Setup
1. **AWS Account** with appropriate permissions
2. **AWS CLI configured** with credentials
3. **S3 bucket** for SAM deployment artifacts (auto-created)

### Verify Prerequisites
```bash
# Check AWS CLI
aws --version
aws sts get-caller-identity

# Check SAM CLI
sam --version

# Check Node.js
node --version
npm --version

# Check Docker
docker --version
```

## 🌍 Environment Overview

| Environment | Purpose | Auto-Deploy | Approval Required |
|-------------|---------|-------------|-------------------|
| **dev** | Development & testing | ✅ | ❌ |
| **staging** | Pre-production testing | ✅ | ❌ |
| **prod** | Production | ❌ | ✅ |

## 🔧 Configuration Files

### SAM Configuration (`samconfig.toml`)
```toml
[dev.deploy.parameters]
stack_name = "hopie-app-dev"
region = "us-east-1"
parameter_overrides = [
    "Stage=dev",
    "CognitoDomainPrefix=hopie-app-dev"
]
confirm_changeset = false

[prod.deploy.parameters]
stack_name = "hopie-app-prod"
region = "us-east-1"
parameter_overrides = [
    "Stage=prod",
    "CognitoDomainPrefix=hopie-app-prod"
]
confirm_changeset = true
```

### Environment Variables
Each environment has specific configuration in `config/{env}.json`:

```json
{
  "environment": "dev",
  "apiUrl": "https://api-dev.hopie-app.com",
  "webSocketUrl": "wss://ws-dev.hopie-app.com",
  "cloudFrontUrl": "https://cdn-dev.hopie-app.com",
  "cognito": {
    "userPoolId": "us-east-1_xxxxxxxxx",
    "userPoolClientId": "xxxxxxxxxxxxxxxxxxxxxxxxxx",
    "region": "us-east-1"
  }
}
```

## 🚀 Deployment Process

### Quick Deployment
```bash
# Deploy to development
./scripts/deploy.sh dev

# Deploy to staging
./scripts/deploy.sh staging

# Deploy to production
./scripts/deploy.sh prod
```

### Manual Deployment Steps

#### 1. Install Dependencies
```bash
npm install
```

#### 2. Validate Template
```bash
sam validate
```

#### 3. Build Application
```bash
sam build --cached --parallel
```

#### 4. Deploy to Environment
```bash
# Development (no confirmation)
sam deploy --config-env dev --no-confirm-changeset

# Production (with confirmation)
sam deploy --config-env prod --confirm-changeset
```

#### 5. Verify Deployment
```bash
# Get stack outputs
aws cloudformation describe-stacks \
  --stack-name hopie-app-dev \
  --query 'Stacks[0].Outputs'
```

## 🔍 Post-Deployment Verification

### 1. API Gateway Health Check
```bash
# Get API URL from stack outputs
API_URL=$(aws cloudformation describe-stacks \
  --stack-name hopie-app-dev \
  --query 'Stacks[0].Outputs[?OutputKey==`ApiGatewayUrl`].OutputValue' \
  --output text)

# Test API health
curl $API_URL/health
```

### 2. DynamoDB Table Verification
```bash
# List tables
aws dynamodb list-tables

# Describe table
aws dynamodb describe-table --table-name HopieApp-dev
```

### 3. Cognito User Pool Verification
```bash
# Get User Pool ID
USER_POOL_ID=$(aws cloudformation describe-stacks \
  --stack-name hopie-app-dev \
  --query 'Stacks[0].Outputs[?OutputKey==`UserPoolId`].OutputValue' \
  --output text)

# Describe user pool
aws cognito-idp describe-user-pool --user-pool-id $USER_POOL_ID
```

### 4. S3 Buckets Verification
```bash
# List buckets
aws s3 ls | grep hopie

# Check bucket policies
aws s3api get-bucket-policy --bucket hopie-user-content-dev
```

### 5. CloudFront Distribution
```bash
# Get CloudFront URL
CLOUDFRONT_URL=$(aws cloudformation describe-stacks \
  --stack-name hopie-app-dev \
  --query 'Stacks[0].Outputs[?OutputKey==`CloudFrontUrl`].OutputValue' \
  --output text)

# Test CloudFront
curl -I $CLOUDFRONT_URL
```

## 🔄 CI/CD Pipeline

### GitHub Actions Workflow
```yaml
name: Deploy HopieApp

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm test

  deploy-dev:
    if: github.ref == 'refs/heads/develop'
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: aws-actions/setup-sam@v2
      - uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1
      - run: ./scripts/deploy.sh dev

  deploy-prod:
    if: github.ref == 'refs/heads/main'
    needs: test
    runs-on: ubuntu-latest
    environment: production
    steps:
      - uses: actions/checkout@v3
      - uses: aws-actions/setup-sam@v2
      - uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1
      - run: ./scripts/deploy.sh prod
```

## 🔧 Troubleshooting

### Common Issues

#### 1. SAM Build Failures
```bash
# Clear SAM cache
sam build --cached --parallel

# Build without cache
sam build --no-cached
```

#### 2. DynamoDB Permission Errors
```bash
# Check IAM policies
aws iam list-attached-role-policies --role-name hopie-app-dev-AuthFunctionRole-*
```

#### 3. Cognito Domain Already Exists
```bash
# Check existing domains
aws cognito-idp list-domains

# Use different domain prefix
sam deploy --parameter-overrides CognitoDomainPrefix=hopie-app-dev-v2
```

#### 4. S3 Bucket Name Conflicts
```bash
# Buckets are globally unique, check if exists
aws s3 ls s3://hopie-user-content-dev

# Use account ID in bucket name (already implemented)
```

#### 5. CloudFormation Stack Rollback
```bash
# Check stack events
aws cloudformation describe-stack-events --stack-name hopie-app-dev

# Delete failed stack
aws cloudformation delete-stack --stack-name hopie-app-dev
```

### Debugging Commands

#### View Lambda Logs
```bash
# Real-time logs
sam logs -n AuthFunction --stack-name hopie-app-dev --tail

# Specific time range
sam logs -n AuthFunction --stack-name hopie-app-dev \
  --start-time '2024-01-15T10:00:00' \
  --end-time '2024-01-15T11:00:00'
```

#### Check API Gateway Logs
```bash
# Enable logging in API Gateway
aws apigateway put-stage \
  --rest-api-id YOUR_API_ID \
  --stage-name dev \
  --patch-ops op=replace,path=/accessLogSettings/destinationArn,value=arn:aws:logs:us-east-1:ACCOUNT:log-group:API-Gateway-Execution-Logs
```

#### Monitor DynamoDB Metrics
```bash
# Get table metrics
aws cloudwatch get-metric-statistics \
  --namespace AWS/DynamoDB \
  --metric-name ConsumedReadCapacityUnits \
  --dimensions Name=TableName,Value=HopieApp-dev \
  --start-time 2024-01-15T00:00:00Z \
  --end-time 2024-01-15T23:59:59Z \
  --period 3600 \
  --statistics Sum
```

## 🔒 Security Considerations

### 1. IAM Permissions
- Use least privilege principle
- Separate roles for each Lambda function
- Regular permission audits

### 2. API Security
- Enable API Gateway throttling
- Use Cognito authorizers
- Implement CORS properly

### 3. Data Protection
- Enable DynamoDB encryption at rest
- Use S3 bucket encryption
- Implement proper access controls

### 4. Secrets Management
- Use AWS Secrets Manager for sensitive data
- Rotate credentials regularly
- Never commit secrets to code

## 📊 Monitoring & Alerting

### CloudWatch Alarms
```bash
# Create error rate alarm
aws cloudwatch put-metric-alarm \
  --alarm-name "HopieApp-HighErrorRate" \
  --alarm-description "High error rate in Lambda functions" \
  --metric-name Errors \
  --namespace AWS/Lambda \
  --statistic Sum \
  --period 300 \
  --threshold 10 \
  --comparison-operator GreaterThanThreshold
```

### Dashboard Setup
- Lambda function metrics
- DynamoDB performance
- API Gateway latency
- User activity metrics

## 🔄 Rollback Procedures

### Automatic Rollback
SAM automatically rolls back failed deployments.

### Manual Rollback
```bash
# List stack events to find last successful deployment
aws cloudformation describe-stack-events --stack-name hopie-app-prod

# Rollback to previous version
aws cloudformation cancel-update-stack --stack-name hopie-app-prod
```

### Database Rollback
- Use DynamoDB Point-in-Time Recovery
- Restore from backup if needed

## 📈 Performance Optimization

### Lambda Optimization
- Use provisioned concurrency for critical functions
- Optimize memory allocation
- Minimize cold starts

### DynamoDB Optimization
- Use appropriate read/write capacity
- Implement efficient access patterns
- Monitor hot partitions

### API Gateway Optimization
- Enable caching where appropriate
- Use compression
- Implement proper throttling

## 🎯 Best Practices

1. **Version Control**: Tag releases and maintain changelog
2. **Testing**: Run comprehensive tests before deployment
3. **Monitoring**: Set up proper monitoring and alerting
4. **Documentation**: Keep deployment docs updated
5. **Security**: Regular security audits and updates
6. **Backup**: Regular backups of critical data
7. **Cost Optimization**: Monitor and optimize AWS costs
