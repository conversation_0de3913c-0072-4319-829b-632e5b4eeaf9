const response = require('../shared/response');
const auth = require('../shared/auth');
const database = require('../shared/database');

/**
 * Stats Service - Handle statistics and analytics
 */

exports.handler = async (event, context) => {
  console.log('Stats service event:', JSON.stringify(event, null, 2));

  try {
    const { httpMethod, path, pathParameters } = event;
    const body = event.body ? JSON.parse(event.body) : {};

    // Handle CORS preflight
    if (httpMethod === 'OPTIONS') {
      return response.corsResponse();
    }

    // Extract user from token
    const user = auth.extractUserFromToken(event);
    if (!user) {
      return response.unauthorized();
    }

    // Route to appropriate handler
    const route = `${httpMethod} ${path}`;
    
    switch (route) {
      case 'GET /stats/streak':
        return await handleGetStreak(user);
      
      case 'GET /stats/couple-activity':
        return await handleGetCoupleActivity(user);
      
      case 'GET /stats/monthly':
        return await handleGetMonthlyStats(user);
      
      case 'GET /stats/dashboard':
        return await handleGetDashboard(user);
      
      default:
        return response.notFound('Endpoint not found');
    }
  } catch (error) {
    console.error('Stats service error:', error);
    return response.handleError(error);
  }
};

async function handleGetStreak(user) {
  try {
    const currentMonth = new Date().toISOString().substring(0, 7);
    const streak = await database.get(`USER#${user.userId}`, `STREAK#${currentMonth}`);
    
    return response.success({
      streak: streak || {
        currentStreak: 0,
        longestStreak: 0,
        totalActiveDays: 0
      }
    });
  } catch (error) {
    return response.handleError(error);
  }
}

async function handleGetCoupleActivity(user) {
  // Implementation for getting couple activity
  return response.success({ activity: [] });
}

async function handleGetMonthlyStats(user) {
  // Implementation for getting monthly stats
  return response.success({ stats: {} });
}

async function handleGetDashboard(user) {
  // Implementation for getting dashboard data
  return response.success({ dashboard: {} });
}
