{"TableName": "<PERSON><PERSON><PERSON><PERSON>", "AttributeDefinitions": [{"AttributeName": "PK", "AttributeType": "S"}, {"AttributeName": "SK", "AttributeType": "S"}, {"AttributeName": "GSI1PK", "AttributeType": "S"}, {"AttributeName": "GSI1SK", "AttributeType": "S"}, {"AttributeName": "GSI2PK", "AttributeType": "S"}, {"AttributeName": "GSI2SK", "AttributeType": "S"}], "KeySchema": [{"AttributeName": "PK", "KeyType": "HASH"}, {"AttributeName": "SK", "KeyType": "RANGE"}], "BillingMode": "PAY_PER_REQUEST", "GlobalSecondaryIndexes": [{"IndexName": "GSI1", "KeySchema": [{"AttributeName": "GSI1PK", "KeyType": "HASH"}, {"AttributeName": "GSI1SK", "KeyType": "RANGE"}], "Projection": {"ProjectionType": "ALL"}}, {"IndexName": "GSI2", "KeySchema": [{"AttributeName": "GSI2PK", "KeyType": "HASH"}, {"AttributeName": "GSI2SK", "KeyType": "RANGE"}], "Projection": {"ProjectionType": "ALL"}}], "StreamSpecification": {"StreamEnabled": true, "StreamViewType": "NEW_AND_OLD_IMAGES"}, "PointInTimeRecoverySpecification": {"PointInTimeRecoveryEnabled": true}, "Tags": [{"Key": "Application", "Value": "<PERSON><PERSON><PERSON><PERSON>"}, {"Key": "Environment", "Value": "production"}]}