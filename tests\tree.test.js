const { handler } = require('../src/tree/index');

describe('Tree Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /trees/types', () => {
    it('should return all tree types', async () => {
      const event = mockEvent({
        httpMethod: 'GET',
        path: '/trees/types'
      });

      const result = await handler(event, mockContext());

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(true);
      expect(body.data).toBeDefined();
      expect(body.data.roble).toBeDefined();
      expect(body.data.cerezo).toBeDefined();
      expect(body.data.pino).toBeDefined();
      expect(body.data.sauce).toBeDefined();
      expect(body.data.olivo).toBeDefined();
    });
  });

  describe('POST /trees/select', () => {
    it('should select tree type successfully', async () => {
      const event = mockEvent({
        httpMethod: 'POST',
        path: '/trees/select',
        body: JSON.stringify({
          treeType: 'roble'
        })
      });

      // Mock user with couple
      const AWS = require('aws-sdk');
      const mockDynamoDB = AWS.DynamoDB.DocumentClient();
      mockDynamoDB.get.mockReturnValueOnce({
        promise: jest.fn().mockResolvedValue({
          Item: {
            userId: 'test-user-id',
            coupleId: 'test-couple-id'
          }
        })
      });

      const result = await handler(event, mockContext());

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(true);
      expect(body.data.treeType).toBe('roble');
    });

    it('should return error for user not in couple', async () => {
      const event = mockEvent({
        httpMethod: 'POST',
        path: '/trees/select',
        body: JSON.stringify({
          treeType: 'roble'
        })
      });

      // Mock user without couple
      const AWS = require('aws-sdk');
      const mockDynamoDB = AWS.DynamoDB.DocumentClient();
      mockDynamoDB.get.mockReturnValueOnce({
        promise: jest.fn().mockResolvedValue({
          Item: {
            userId: 'test-user-id'
            // No coupleId
          }
        })
      });

      const result = await handler(event, mockContext());

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
    });

    it('should return validation error for invalid tree type', async () => {
      const event = mockEvent({
        httpMethod: 'POST',
        path: '/trees/select',
        body: JSON.stringify({
          treeType: 'invalid-tree'
        })
      });

      const result = await handler(event, mockContext());

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
    });
  });

  describe('GET /trees/progress', () => {
    it('should return tree progress', async () => {
      const event = mockEvent({
        httpMethod: 'GET',
        path: '/trees/progress'
      });

      // Mock user with couple and tree progress
      const AWS = require('aws-sdk');
      const mockDynamoDB = AWS.DynamoDB.DocumentClient();
      
      mockDynamoDB.get
        .mockReturnValueOnce({
          promise: jest.fn().mockResolvedValue({
            Item: {
              userId: 'test-user-id',
              coupleId: 'test-couple-id'
            }
          })
        })
        .mockReturnValueOnce({
          promise: jest.fn().mockResolvedValue({
            Item: {
              treeType: 'roble',
              level: 15,
              currentStage: 'plantula',
              experiencePoints: 375,
              totalWaterings: 15
            }
          })
        });

      const result = await handler(event, mockContext());

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(true);
      expect(body.data.progress).toBeDefined();
      expect(body.data.progress.level).toBe(15);
      expect(body.data.treeType).toBe('roble');
    });

    it('should return error for user not in couple', async () => {
      const event = mockEvent({
        httpMethod: 'GET',
        path: '/trees/progress'
      });

      // Mock user without couple
      const AWS = require('aws-sdk');
      const mockDynamoDB = AWS.DynamoDB.DocumentClient();
      mockDynamoDB.get.mockReturnValueOnce({
        promise: jest.fn().mockResolvedValue({
          Item: {
            userId: 'test-user-id'
            // No coupleId
          }
        })
      });

      const result = await handler(event, mockContext());

      expect(result.statusCode).toBe(404);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
    });
  });

  describe('POST /trees/water', () => {
    it('should water tree successfully', async () => {
      const event = mockEvent({
        httpMethod: 'POST',
        path: '/trees/water',
        body: JSON.stringify({
          message: 'Watering with love!'
        })
      });

      // Mock user with couple and tree progress
      const AWS = require('aws-sdk');
      const mockDynamoDB = AWS.DynamoDB.DocumentClient();
      
      mockDynamoDB.get
        .mockReturnValueOnce({
          promise: jest.fn().mockResolvedValue({
            Item: {
              userId: 'test-user-id',
              coupleId: 'test-couple-id'
            }
          })
        })
        .mockReturnValueOnce({
          promise: jest.fn().mockResolvedValue({
            Item: null // No watering today
          })
        })
        .mockReturnValueOnce({
          promise: jest.fn().mockResolvedValue({
            Item: {
              treeType: 'roble',
              level: 15,
              currentStage: 'plantula',
              experiencePoints: 375,
              totalWaterings: 15
            }
          })
        });

      const result = await handler(event, mockContext());

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(true);
      expect(body.data.watered).toBe(true);
      expect(body.data.experienceGained).toBe(25);
    });

    it('should return error if already watered today', async () => {
      const event = mockEvent({
        httpMethod: 'POST',
        path: '/trees/water',
        body: JSON.stringify({
          message: 'Watering with love!'
        })
      });

      // Mock user with couple and existing watering today
      const AWS = require('aws-sdk');
      const mockDynamoDB = AWS.DynamoDB.DocumentClient();
      
      mockDynamoDB.get
        .mockReturnValueOnce({
          promise: jest.fn().mockResolvedValue({
            Item: {
              userId: 'test-user-id',
              coupleId: 'test-couple-id'
            }
          })
        })
        .mockReturnValueOnce({
          promise: jest.fn().mockResolvedValue({
            Item: {
              wateringId: 'existing-watering',
              date: new Date().toISOString().split('T')[0]
            }
          })
        });

      const result = await handler(event, mockContext());

      expect(result.statusCode).toBe(409);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
      expect(body.error.message).toContain('already watered');
    });
  });

  describe('Unauthorized requests', () => {
    it('should return unauthorized for missing token', async () => {
      const event = mockEvent({
        httpMethod: 'GET',
        path: '/trees/progress',
        headers: {},
        requestContext: {}
      });

      const result = await handler(event, mockContext());

      expect(result.statusCode).toBe(401);
      const body = JSON.parse(result.body);
      expect(body.success).toBe(false);
    });
  });
});
