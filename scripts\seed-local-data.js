const AWS = require('aws-sdk');

/**
 * Seed local data for development
 * Usage: DYNAMODB_ENDPOINT=http://localhost:8000 node scripts/seed-local-data.js
 */

// Configure AWS for local DynamoDB
AWS.config.update({ 
  region: 'us-east-2',
  endpoint: process.env.DYNAMODB_ENDPOINT || 'http://localhost:8000'
});

const dynamodb = new AWS.DynamoDB.DocumentClient();
const TABLE_NAME = 'HopieApp-local';

async function seedLocalData() {
  console.log('🌱 Seeding local development data...');

  try {
    // Seed tree types
    await seedTreeTypes();
    
    // Seed sample questions
    await seedSampleQuestions();
    
    console.log('✅ Local data seeding completed successfully!');
  } catch (error) {
    console.error('❌ Error seeding local data:', error);
    process.exit(1);
  }
}

/**
 * Seed tree types data
 */
async function seedTreeTypes() {
  console.log('📊 Seeding tree types...');

  const treeTypes = [
    {
      PK: 'TREE_TYPE#roble',
      SK: 'METADATA',
      treeType: 'roble',
      name: 'Roble',
      description: 'Símbolo de fortaleza y resistencia.',
      GSI1PK: 'TYPE#TREE_TYPE',
      GSI1SK: 'TREE_TYPE#roble',
      createdAt: new Date().toISOString()
    },
    {
      PK: 'TREE_TYPE#cerezo',
      SK: 'METADATA',
      treeType: 'cerezo',
      name: 'Cerezo',
      description: 'Representa la belleza efímera y la renovación.',
      GSI1PK: 'TYPE#TREE_TYPE',
      GSI1SK: 'TREE_TYPE#cerezo',
      createdAt: new Date().toISOString()
    }
  ];

  for (const treeType of treeTypes) {
    await dynamodb.put({
      TableName: TABLE_NAME,
      Item: treeType
    }).promise();
  }

  console.log(`✅ Seeded ${treeTypes.length} tree types`);
}

/**
 * Seed sample daily questions
 */
async function seedSampleQuestions() {
  console.log('❓ Seeding sample questions...');

  const today = new Date();
  const questions = [];

  // Create questions for the next 3 days
  for (let i = 0; i < 3; i++) {
    const date = new Date(today);
    date.setDate(date.getDate() + i);
    const dateStr = date.toISOString().split('T')[0];

    const sampleQuestions = [
      { category: 'memories', question: '¿Cuál es tu recuerdo favorito de nosotros?' },
      { category: 'dreams', question: '¿Cómo te imaginas nuestro futuro en 5 años?' },
      { category: 'feelings', question: '¿Qué es lo que más admiras de mí?' }
    ];

    const questionData = sampleQuestions[i % sampleQuestions.length];

    const question = {
      PK: `DAILY_QUESTION#${dateStr}`,
      SK: 'QUESTION',
      date: dateStr,
      question: questionData.question,
      category: questionData.category,
      createdAt: new Date().toISOString(),
      GSI1PK: 'TYPE#DAILY_QUESTION',
      GSI1SK: `DATE#${dateStr}`
    };

    await dynamodb.put({
      TableName: TABLE_NAME,
      Item: question
    }).promise();

    questions.push(question);
  }

  console.log(`✅ Seeded ${questions.length} sample questions`);
}

// Run the seeding
seedLocalData();
