const AWS = require('aws-sdk');
const jwt = require('jsonwebtoken');

const cognito = new AWS.CognitoIdentityServiceProvider({
  region: process.env.AWS_REGION || 'us-east-2'
});

const USER_POOL_ID = process.env.USER_POOL_ID;
const USER_POOL_CLIENT_ID = process.env.USER_POOL_CLIENT_ID;

/**
 * Authentication utilities for HopieApp
 */

/**
 * Extract user information from Cognito JWT token
 */
function extractUserFromToken(event) {
  try {
    // Check if user is authenticated via API Gateway Cognito authorizer
    if (event.requestContext && event.requestContext.authorizer && event.requestContext.authorizer.claims) {
      const claims = event.requestContext.authorizer.claims;
      return {
        userId: claims.sub,
        email: claims.email,
        name: claims.name || claims.given_name || claims.email,
        emailVerified: claims.email_verified === 'true'
      };
    }

    // Fallback: Extract from Authorization header
    const authHeader = event.headers?.Authorization || event.headers?.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }

    const token = authHeader.substring(7);
    const decoded = jwt.decode(token);
    
    if (!decoded) {
      return null;
    }

    return {
      userId: decoded.sub,
      email: decoded.email,
      name: decoded.name || decoded.given_name || decoded.email,
      emailVerified: decoded.email_verified === 'true'
    };
  } catch (error) {
    console.error('Error extracting user from token:', error);
    return null;
  }
}

/**
 * Verify JWT token with Cognito
 */
async function verifyToken(token) {
  try {
    // For now, we'll rely on API Gateway's Cognito authorizer
    // In production, you might want to add additional verification
    const decoded = jwt.decode(token);
    return decoded;
  } catch (error) {
    console.error('Token verification error:', error);
    throw new Error('Invalid token');
  }
}

/**
 * Register a new user with Cognito
 */
async function registerUser(email, password, name) {
  try {
    const params = {
      ClientId: USER_POOL_CLIENT_ID,
      Username: email,
      Password: password,
      UserAttributes: [
        {
          Name: 'email',
          Value: email
        },
        {
          Name: 'name',
          Value: name
        }
      ]
    };

    const result = await cognito.signUp(params).promise();
    return {
      userId: result.UserSub,
      email,
      name,
      confirmationRequired: !result.UserConfirmed
    };
  } catch (error) {
    console.error('User registration error:', error);
    throw error;
  }
}

/**
 * Confirm user registration
 */
async function confirmUser(email, confirmationCode) {
  try {
    const params = {
      ClientId: USER_POOL_CLIENT_ID,
      Username: email,
      ConfirmationCode: confirmationCode
    };

    await cognito.confirmSignUp(params).promise();
    return { success: true };
  } catch (error) {
    console.error('User confirmation error:', error);
    throw error;
  }
}

/**
 * Authenticate user and get tokens
 */
async function authenticateUser(email, password) {
  try {
    const params = {
      AuthFlow: 'USER_PASSWORD_AUTH',
      ClientId: USER_POOL_CLIENT_ID,
      AuthParameters: {
        USERNAME: email,
        PASSWORD: password
      }
    };

    const result = await cognito.initiateAuth(params).promise();
    
    if (result.ChallengeName) {
      // Handle challenges (MFA, password reset, etc.)
      return {
        challengeName: result.ChallengeName,
        session: result.Session,
        challengeParameters: result.ChallengeParameters
      };
    }

    const tokens = result.AuthenticationResult;
    const userInfo = jwt.decode(tokens.IdToken);

    return {
      accessToken: tokens.AccessToken,
      idToken: tokens.IdToken,
      refreshToken: tokens.RefreshToken,
      expiresIn: tokens.ExpiresIn,
      user: {
        userId: userInfo.sub,
        email: userInfo.email,
        name: userInfo.name || userInfo.given_name || userInfo.email,
        emailVerified: userInfo.email_verified === 'true'
      }
    };
  } catch (error) {
    console.error('Authentication error:', error);
    throw error;
  }
}

/**
 * Refresh access token
 */
async function refreshToken(refreshTokenValue) {
  try {
    const params = {
      AuthFlow: 'REFRESH_TOKEN_AUTH',
      ClientId: USER_POOL_CLIENT_ID,
      AuthParameters: {
        REFRESH_TOKEN: refreshTokenValue
      }
    };

    const result = await cognito.initiateAuth(params).promise();
    const tokens = result.AuthenticationResult;

    return {
      accessToken: tokens.AccessToken,
      idToken: tokens.IdToken,
      expiresIn: tokens.ExpiresIn
    };
  } catch (error) {
    console.error('Token refresh error:', error);
    throw error;
  }
}

/**
 * Update user attributes
 */
async function updateUserAttributes(accessToken, attributes) {
  try {
    const userAttributes = Object.entries(attributes).map(([key, value]) => ({
      Name: key,
      Value: value
    }));

    const params = {
      AccessToken: accessToken,
      UserAttributes: userAttributes
    };

    await cognito.updateUserAttributes(params).promise();
    return { success: true };
  } catch (error) {
    console.error('Update user attributes error:', error);
    throw error;
  }
}

/**
 * Get user information
 */
async function getUser(accessToken) {
  try {
    const params = {
      AccessToken: accessToken
    };

    const result = await cognito.getUser(params).promise();
    
    const attributes = {};
    result.UserAttributes.forEach(attr => {
      attributes[attr.Name] = attr.Value;
    });

    return {
      userId: result.Username,
      email: attributes.email,
      name: attributes.name,
      emailVerified: attributes.email_verified === 'true',
      createdAt: result.UserCreateDate,
      lastModified: result.UserLastModifiedDate,
      status: result.UserStatus
    };
  } catch (error) {
    console.error('Get user error:', error);
    throw error;
  }
}

/**
 * Sign out user
 */
async function signOut(accessToken) {
  try {
    const params = {
      AccessToken: accessToken
    };

    await cognito.globalSignOut(params).promise();
    return { success: true };
  } catch (error) {
    console.error('Sign out error:', error);
    throw error;
  }
}

/**
 * Initiate forgot password flow
 */
async function forgotPassword(email) {
  try {
    const params = {
      ClientId: USER_POOL_CLIENT_ID,
      Username: email
    };

    await cognito.forgotPassword(params).promise();
    return { success: true };
  } catch (error) {
    console.error('Forgot password error:', error);
    throw error;
  }
}

/**
 * Confirm forgot password with new password
 */
async function confirmForgotPassword(email, confirmationCode, newPassword) {
  try {
    const params = {
      ClientId: USER_POOL_CLIENT_ID,
      Username: email,
      ConfirmationCode: confirmationCode,
      Password: newPassword
    };

    await cognito.confirmForgotPassword(params).promise();
    return { success: true };
  } catch (error) {
    console.error('Confirm forgot password error:', error);
    throw error;
  }
}

/**
 * Middleware to require authentication
 */
function requireAuth(handler) {
  return async (event, context) => {
    try {
      const user = extractUserFromToken(event);
      if (!user) {
        return {
          statusCode: 401,
          headers: {
            'Access-Control-Allow-Origin': '*',
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            success: false,
            error: {
              message: 'Authentication required',
              code: 'UNAUTHORIZED'
            }
          })
        };
      }

      // Add user to event for use in handler
      event.user = user;
      
      return await handler(event, context);
    } catch (error) {
      console.error('Auth middleware error:', error);
      return {
        statusCode: 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          success: false,
          error: {
            message: 'Internal server error',
            code: 'INTERNAL_ERROR'
          }
        })
      };
    }
  };
}

module.exports = {
  extractUserFromToken,
  verifyToken,
  registerUser,
  confirmUser,
  authenticateUser,
  refreshToken,
  updateUserAttributes,
  getUser,
  signOut,
  forgotPassword,
  confirmForgotPassword,
  requireAuth
};
