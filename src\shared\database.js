const AWS = require('aws-sdk');

// Configure DynamoDB
const dynamodb = new AWS.DynamoDB.DocumentClient({
  region: process.env.AWS_REGION || 'us-east-2'
});

const TABLE_NAME = process.env.DYNAMODB_TABLE;

/**
 * Database utility class for HopieApp
 */
class Database {
  constructor() {
    this.tableName = TABLE_NAME;
  }

  /**
   * Get a single item by PK and SK
   */
  async get(pk, sk) {
    try {
      const params = {
        TableName: this.tableName,
        Key: { PK: pk, SK: sk }
      };

      const result = await dynamodb.get(params).promise();
      return result.Item || null;
    } catch (error) {
      console.error('Database get error:', error);
      throw error;
    }
  }

  /**
   * Put an item into the table
   */
  async put(item) {
    try {
      const params = {
        TableName: this.tableName,
        Item: {
          ...item,
          createdAt: item.createdAt || new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      };

      await dynamodb.put(params).promise();
      return params.Item;
    } catch (error) {
      console.error('Database put error:', error);
      throw error;
    }
  }

  /**
   * Update an item in the table
   */
  async update(pk, sk, updateExpression, expressionAttributeValues, expressionAttributeNames = {}) {
    try {
      const params = {
        TableName: this.tableName,
        Key: { PK: pk, SK: sk },
        UpdateExpression: updateExpression,
        ExpressionAttributeValues: {
          ...expressionAttributeValues,
          ':updatedAt': new Date().toISOString()
        },
        ExpressionAttributeNames: expressionAttributeNames,
        ReturnValues: 'ALL_NEW'
      };

      // Add updatedAt to the update expression if not already present
      if (!updateExpression.includes('updatedAt')) {
        params.UpdateExpression += ', updatedAt = :updatedAt';
      }

      const result = await dynamodb.update(params).promise();
      return result.Attributes;
    } catch (error) {
      console.error('Database update error:', error);
      throw error;
    }
  }

  /**
   * Delete an item from the table
   */
  async delete(pk, sk) {
    try {
      const params = {
        TableName: this.tableName,
        Key: { PK: pk, SK: sk },
        ReturnValues: 'ALL_OLD'
      };

      const result = await dynamodb.delete(params).promise();
      return result.Attributes;
    } catch (error) {
      console.error('Database delete error:', error);
      throw error;
    }
  }

  /**
   * Query items by PK with optional SK condition
   */
  async query(pk, skCondition = null, options = {}) {
    try {
      const params = {
        TableName: this.tableName,
        KeyConditionExpression: 'PK = :pk',
        ExpressionAttributeValues: { ':pk': pk },
        ...options
      };

      if (skCondition) {
        params.KeyConditionExpression += ` AND ${skCondition.expression}`;
        Object.assign(params.ExpressionAttributeValues, skCondition.values);
      }

      const result = await dynamodb.query(params).promise();
      return {
        items: result.Items || [],
        lastEvaluatedKey: result.LastEvaluatedKey,
        count: result.Count
      };
    } catch (error) {
      console.error('Database query error:', error);
      throw error;
    }
  }

  /**
   * Query using GSI1
   */
  async queryGSI1(gsi1pk, gsi1skCondition = null, options = {}) {
    try {
      const params = {
        TableName: this.tableName,
        IndexName: 'GSI1',
        KeyConditionExpression: 'GSI1PK = :gsi1pk',
        ExpressionAttributeValues: { ':gsi1pk': gsi1pk },
        ...options
      };

      if (gsi1skCondition) {
        params.KeyConditionExpression += ` AND ${gsi1skCondition.expression}`;
        Object.assign(params.ExpressionAttributeValues, gsi1skCondition.values);
      }

      const result = await dynamodb.query(params).promise();
      return {
        items: result.Items || [],
        lastEvaluatedKey: result.LastEvaluatedKey,
        count: result.Count
      };
    } catch (error) {
      console.error('Database queryGSI1 error:', error);
      throw error;
    }
  }

  /**
   * Query using GSI2
   */
  async queryGSI2(gsi2pk, gsi2skCondition = null, options = {}) {
    try {
      const params = {
        TableName: this.tableName,
        IndexName: 'GSI2',
        KeyConditionExpression: 'GSI2PK = :gsi2pk',
        ExpressionAttributeValues: { ':gsi2pk': gsi2pk },
        ...options
      };

      if (gsi2skCondition) {
        params.KeyConditionExpression += ` AND ${gsi2skCondition.expression}`;
        Object.assign(params.ExpressionAttributeValues, gsi2skCondition.values);
      }

      const result = await dynamodb.query(params).promise();
      return {
        items: result.Items || [],
        lastEvaluatedKey: result.LastEvaluatedKey,
        count: result.Count
      };
    } catch (error) {
      console.error('Database queryGSI2 error:', error);
      throw error;
    }
  }

  /**
   * Batch get items
   */
  async batchGet(keys) {
    try {
      const params = {
        RequestItems: {
          [this.tableName]: {
            Keys: keys
          }
        }
      };

      const result = await dynamodb.batchGet(params).promise();
      return result.Responses[this.tableName] || [];
    } catch (error) {
      console.error('Database batchGet error:', error);
      throw error;
    }
  }

  /**
   * Batch write items (put/delete)
   */
  async batchWrite(putItems = [], deleteKeys = []) {
    try {
      const requestItems = [];

      // Add put requests
      putItems.forEach(item => {
        requestItems.push({
          PutRequest: {
            Item: {
              ...item,
              createdAt: item.createdAt || new Date().toISOString(),
              updatedAt: new Date().toISOString()
            }
          }
        });
      });

      // Add delete requests
      deleteKeys.forEach(key => {
        requestItems.push({
          DeleteRequest: { Key: key }
        });
      });

      if (requestItems.length === 0) {
        return { success: true };
      }

      const params = {
        RequestItems: {
          [this.tableName]: requestItems
        }
      };

      await dynamodb.batchWrite(params).promise();
      return { success: true };
    } catch (error) {
      console.error('Database batchWrite error:', error);
      throw error;
    }
  }

  /**
   * Scan table (use sparingly)
   */
  async scan(filterExpression = null, expressionAttributeValues = {}, options = {}) {
    try {
      const params = {
        TableName: this.tableName,
        ...options
      };

      if (filterExpression) {
        params.FilterExpression = filterExpression;
        params.ExpressionAttributeValues = expressionAttributeValues;
      }

      const result = await dynamodb.scan(params).promise();
      return {
        items: result.Items || [],
        lastEvaluatedKey: result.LastEvaluatedKey,
        count: result.Count
      };
    } catch (error) {
      console.error('Database scan error:', error);
      throw error;
    }
  }

  /**
   * Transaction write
   */
  async transactWrite(transactItems) {
    try {
      const params = {
        TransactItems: transactItems
      };

      await dynamodb.transactWrite(params).promise();
      return { success: true };
    } catch (error) {
      console.error('Database transactWrite error:', error);
      throw error;
    }
  }

  /**
   * Transaction read
   */
  async transactGet(transactItems) {
    try {
      const params = {
        TransactItems: transactItems
      };

      const result = await dynamodb.transactGet(params).promise();
      return result.Responses.map(response => response.Item);
    } catch (error) {
      console.error('Database transactGet error:', error);
      throw error;
    }
  }
}

module.exports = new Database();
