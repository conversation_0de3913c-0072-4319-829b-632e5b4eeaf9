# 🚀 HopieApp - Inici<PERSON>

## ⚡ Despliegue en 5 Minutos

### 1. Prerrequisitos Rápidos

```bash
# Verificar que tienes todo instalado
aws --version    # AWS CLI
sam --version    # SAM CLI  
node --version   # Node.js 18+

# Configurar AWS (si no lo has hecho)
aws configure
# Region: us-east-2
```

### 2. <PERSON><PERSON><PERSON> y <PERSON>

```bash
# Validar proyecto
./scripts/validate.sh

# Desplegar a desarrollo
./scripts/deploy.sh dev
```

### 3. URLs Generadas

Después del despliegue verás:
- **API**: `https://xxx.execute-api.us-east-2.amazonaws.com/dev`
- **WebSocket**: `wss://xxx.execute-api.us-east-2.amazonaws.com/dev`
- **CDN**: `https://xxx.cloudfront.net`

### 4. Probar la API

```bash
# Obtener tipos de árboles
curl https://tu-api-url/trees/types

# Registrar usuario
curl -X POST https://tu-api-url/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "TestPass123",
    "name": "Test User",
    "confirmPassword": "TestPass123"
  }'
```

## 📱 Configuración Frontend

Usa el archivo generado `config/dev.json`:

```javascript
const config = {
  apiUrl: "https://xxx.execute-api.us-east-2.amazonaws.com/dev",
  webSocketUrl: "wss://xxx.execute-api.us-east-2.amazonaws.com/dev",
  cognito: {
    userPoolId: "us-east-2_xxx",
    userPoolClientId: "xxx",
    region: "us-east-2"
  }
};
```

## 🔧 Comandos Útiles

```bash
# Ver logs en tiempo real
sam logs -n AuthFunction --stack-name hopie-app-dev --tail

# Limpiar todo
./scripts/cleanup.sh dev

# Desarrollo local
./scripts/local-dev.sh
sam local start-api --port 3000
```

## 🆘 Problemas Comunes

**Error de permisos AWS**: Verifica `aws sts get-caller-identity`

**Stack ya existe**: Ejecuta `./scripts/cleanup.sh dev` primero

**Bucket ya existe**: Los nombres incluyen ID de cuenta, debería ser único

## 📚 Documentación Completa

- [Guía de Despliegue Detallada](DEPLOYMENT_GUIDE.md)
- [Documentación de API](docs/api.md)
- [Arquitectura del Backend](BACKEND_ARCHITECTURE.md)

¡Listo! Tu backend de HopieApp está funcionando en AWS 🎉
