const { validateBody } = require('../shared/validation');
const { schemas } = require('../shared/validation');
const response = require('../shared/response');
const auth = require('../shared/auth');
const database = require('../shared/database');
const { v4: uuidv4 } = require('uuid');

/**
 * Life Plan Service - Handle life plan stages and goals
 */

exports.handler = async (event, context) => {
  console.log('Life Plan service event:', JSON.stringify(event, null, 2));

  try {
    const { httpMethod, path, pathParameters } = event;
    const body = event.body ? JSON.parse(event.body) : {};

    // Handle CORS preflight
    if (httpMethod === 'OPTIONS') {
      return response.corsResponse();
    }

    // Extract user from token
    const user = auth.extractUserFromToken(event);
    if (!user) {
      return response.unauthorized();
    }

    // Route to appropriate handler
    const route = `${httpMethod} ${path}`;
    
    switch (route) {
      case 'GET /lifeplan/stages':
        return await handleGetStages(user);
      
      case 'POST /lifeplan/stage':
        return await handleCreateStage(user, body);
      
      case 'PUT /lifeplan/stage':
        return await handleUpdateStage(user, pathParameters, body);
      
      case 'DELETE /lifeplan/stage':
        return await handleDeleteStage(user, pathParameters);
      
      default:
        return response.notFound('Endpoint not found');
    }
  } catch (error) {
    console.error('Life Plan service error:', error);
    return response.handleError(error);
  }
};

async function handleGetStages(user) {
  try {
    const userProfile = await database.get(`USER#${user.userId}`, 'PROFILE');
    if (!userProfile || !userProfile.coupleId) {
      return response.notFound('User is not part of a couple');
    }

    const stages = await database.query(
      `COUPLE#${userProfile.coupleId}`,
      {
        expression: 'begins_with(SK, :sk)',
        values: { ':sk': 'LIFEPLAN#' }
      }
    );

    return response.success({
      stages: stages.items.sort((a, b) => (a.order || 0) - (b.order || 0))
    });
  } catch (error) {
    return response.handleError(error);
  }
}

async function handleCreateStage(user, body) {
  const validation = validateBody(body, schemas.lifePlan.createStage);
  if (!validation.isValid) {
    return response.validationError(validation.errors);
  }

  try {
    const userProfile = await database.get(`USER#${user.userId}`, 'PROFILE');
    if (!userProfile || !userProfile.coupleId) {
      return response.notFound('User is not part of a couple');
    }

    const stageId = uuidv4();
    const stage = {
      PK: `COUPLE#${userProfile.coupleId}`,
      SK: `LIFEPLAN#${stageId}`,
      stageId,
      ...validation.value,
      status: 'pending',
      createdBy: user.userId,
      GSI1PK: 'TYPE#LIFEPLAN_STAGE',
      GSI1SK: `COUPLE#${userProfile.coupleId}#${stageId}`
    };

    await database.put(stage);
    return response.created(stage);
  } catch (error) {
    return response.handleError(error);
  }
}

async function handleUpdateStage(user, pathParameters, body) {
  // Implementation for updating life plan stage
  return response.success({ message: 'Stage updated successfully' });
}

async function handleDeleteStage(user, pathParameters) {
  // Implementation for deleting life plan stage
  return response.success({ message: 'Stage deleted successfully' });
}
