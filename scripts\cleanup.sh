#!/bin/bash

# Cleanup Script for HopieApp
# Usage: ./scripts/cleanup.sh [dev|staging|prod]

set -e

ENVIRONMENT=${1:-dev}

echo "🧹 Cleaning up HopieApp $ENVIRONMENT environment..."

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(dev|staging|prod)$ ]]; then
    echo "❌ Error: Environment must be dev, staging, or prod"
    exit 1
fi

# Confirm deletion for production
if [ "$ENVIRONMENT" = "prod" ]; then
    echo "⚠️  WARNING: You are about to delete the PRODUCTION environment!"
    echo "This action cannot be undone and will delete all data."
    read -p "Type 'DELETE PRODUCTION' to confirm: " confirmation
    
    if [ "$confirmation" != "DELETE PRODUCTION" ]; then
        echo "❌ Cleanup cancelled"
        exit 1
    fi
fi

STACK_NAME="hopie-app-$ENVIRONMENT"

echo "🔍 Checking if stack exists..."
if ! aws cloudformation describe-stacks --stack-name $STACK_NAME &> /dev/null; then
    echo "ℹ️  Stack $STACK_NAME does not exist"
    exit 0
fi

echo "📋 Getting stack resources..."

# Get S3 buckets from stack
USER_CONTENT_BUCKET=$(aws cloudformation describe-stack-resources \
    --stack-name $STACK_NAME \
    --logical-resource-id UserContentBucket \
    --query 'StackResources[0].PhysicalResourceId' \
    --output text 2>/dev/null || echo "")

APP_ASSETS_BUCKET=$(aws cloudformation describe-stack-resources \
    --stack-name $STACK_NAME \
    --logical-resource-id AppAssetsBucket \
    --query 'StackResources[0].PhysicalResourceId' \
    --output text 2>/dev/null || echo "")

# Empty S3 buckets before deletion
if [ ! -z "$USER_CONTENT_BUCKET" ] && [ "$USER_CONTENT_BUCKET" != "None" ]; then
    echo "🗑️  Emptying user content bucket: $USER_CONTENT_BUCKET"
    aws s3 rm s3://$USER_CONTENT_BUCKET --recursive || true
fi

if [ ! -z "$APP_ASSETS_BUCKET" ] && [ "$APP_ASSETS_BUCKET" != "None" ]; then
    echo "🗑️  Emptying app assets bucket: $APP_ASSETS_BUCKET"
    aws s3 rm s3://$APP_ASSETS_BUCKET --recursive || true
fi

# Delete CloudFormation stack
echo "🗑️  Deleting CloudFormation stack: $STACK_NAME"
aws cloudformation delete-stack --stack-name $STACK_NAME

echo "⏳ Waiting for stack deletion to complete..."
aws cloudformation wait stack-delete-complete --stack-name $STACK_NAME

# Clean up local configuration
echo "🧹 Cleaning up local configuration..."
if [ -f "config/$ENVIRONMENT.json" ]; then
    rm "config/$ENVIRONMENT.json"
    echo "✅ Removed config/$ENVIRONMENT.json"
fi

# Clean up SAM build artifacts
echo "🧹 Cleaning up build artifacts..."
if [ -d ".aws-sam" ]; then
    rm -rf .aws-sam
    echo "✅ Removed .aws-sam directory"
fi

echo "✅ Cleanup completed successfully!"
echo ""
echo "🗑️  Deleted resources:"
echo "   - CloudFormation stack: $STACK_NAME"
if [ ! -z "$USER_CONTENT_BUCKET" ] && [ "$USER_CONTENT_BUCKET" != "None" ]; then
    echo "   - S3 bucket: $USER_CONTENT_BUCKET"
fi
if [ ! -z "$APP_ASSETS_BUCKET" ] && [ "$APP_ASSETS_BUCKET" != "None" ]; then
    echo "   - S3 bucket: $APP_ASSETS_BUCKET"
fi
echo "   - All Lambda functions"
echo "   - DynamoDB table"
echo "   - Cognito User Pool"
echo "   - CloudFront distribution"
echo "   - API Gateway"
echo ""
echo "⚠️  Note: Some resources like CloudFront distributions may take up to 15 minutes to fully delete."
