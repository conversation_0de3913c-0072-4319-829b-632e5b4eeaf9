#!/bin/bash

# HopieApp Deployment Script
# Usage: ./scripts/deploy.sh [dev|staging|prod]

set -e

# Default to dev environment
ENVIRONMENT=${1:-dev}

echo "🚀 Deploying HopieApp to $ENVIRONMENT environment..."

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(dev|staging|prod)$ ]]; then
    echo "❌ Error: Environment must be dev, staging, or prod"
    exit 1
fi

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    echo "❌ Error: AWS CLI is not installed"
    exit 1
fi

# Check if SAM CLI is installed
if ! command -v sam &> /dev/null; then
    echo "❌ Error: SAM CLI is not installed"
    exit 1
fi

# Check AWS credentials
if ! aws sts get-caller-identity &> /dev/null; then
    echo "❌ Error: AWS credentials not configured"
    exit 1
fi

echo "✅ Prerequisites check passed"

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Validate SAM template
echo "🔍 Validating SAM template..."
sam validate

# Build the application
echo "🔨 Building application..."
sam build --cached --parallel

# Deploy based on environment
echo "🚀 Deploying to $ENVIRONMENT..."

if [ "$ENVIRONMENT" = "prod" ]; then
    # Production deployment with confirmation
    sam deploy --config-env $ENVIRONMENT --confirm-changeset
else
    # Development/staging deployment without confirmation
    sam deploy --config-env $ENVIRONMENT --no-confirm-changeset
fi

# Get stack outputs
echo "📋 Getting stack outputs..."
STACK_NAME="hopie-app-$ENVIRONMENT"
API_URL=$(aws cloudformation describe-stacks --stack-name $STACK_NAME --query 'Stacks[0].Outputs[?OutputKey==`ApiGatewayUrl`].OutputValue' --output text)
WEBSOCKET_URL=$(aws cloudformation describe-stacks --stack-name $STACK_NAME --query 'Stacks[0].Outputs[?OutputKey==`WebSocketUrl`].OutputValue' --output text)
CLOUDFRONT_URL=$(aws cloudformation describe-stacks --stack-name $STACK_NAME --query 'Stacks[0].Outputs[?OutputKey==`CloudFrontUrl`].OutputValue' --output text)
USER_POOL_ID=$(aws cloudformation describe-stacks --stack-name $STACK_NAME --query 'Stacks[0].Outputs[?OutputKey==`UserPoolId`].OutputValue' --output text)
USER_POOL_CLIENT_ID=$(aws cloudformation describe-stacks --stack-name $STACK_NAME --query 'Stacks[0].Outputs[?OutputKey==`UserPoolClientId`].OutputValue' --output text)

# Create environment configuration file
echo "📝 Creating environment configuration..."
cat > "config/$ENVIRONMENT.json" << EOF
{
  "environment": "$ENVIRONMENT",
  "apiUrl": "$API_URL",
  "webSocketUrl": "$WEBSOCKET_URL",
  "cloudFrontUrl": "$CLOUDFRONT_URL",
  "cognito": {
    "userPoolId": "$USER_POOL_ID",
    "userPoolClientId": "$USER_POOL_CLIENT_ID",
    "region": "us-east-1"
  },
  "deployedAt": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")"
}
EOF

echo "✅ Deployment completed successfully!"
echo ""
echo "🌐 API Gateway URL: $API_URL"
echo "🔌 WebSocket URL: $WEBSOCKET_URL"
echo "📦 CloudFront URL: $CLOUDFRONT_URL"
echo "👤 User Pool ID: $USER_POOL_ID"
echo "🔑 User Pool Client ID: $USER_POOL_CLIENT_ID"
echo ""
echo "📁 Configuration saved to: config/$ENVIRONMENT.json"

# Seed initial data for dev environment
if [ "$ENVIRONMENT" = "dev" ]; then
    echo "🌱 Seeding initial data for development..."
    node scripts/seed-data.js $ENVIRONMENT
fi

echo "🎉 HopieApp deployment to $ENVIRONMENT completed!"
