const { validateBody } = require('../shared/validation');
const { schemas } = require('../shared/validation');
const response = require('../shared/response');
const auth = require('../shared/auth');
const database = require('../shared/database');
const { v4: uuidv4 } = require('uuid');

/**
 * Tree Service - Handle tree growth, watering, and progress
 */

// Tree configuration
const TREE_TYPES = {
  roble: {
    name: '<PERSON><PERSON>',
    description: 'Símbolo de fortaleza y resistencia. Representa una relación sólida que crece lentamente pero con raíces profundas.',
    spiritualMeaning: 'El roble simboliza la sabiduría, la protección y la longevidad en el amor.',
    stages: {
      semilla: { minLevel: 0, maxLevel: 6, imageUrl: 'trees/roble/semilla.png' },
      brote: { minLevel: 7, maxLevel: 20, imageUrl: 'trees/roble/brote.png' },
      plantula: { minLevel: 21, maxLevel: 49, imageUrl: 'trees/roble/plantula.png' },
      arbolJoven: { minLevel: 50, maxLevel: 99, imageUrl: 'trees/roble/arbolJoven.png' },
      arbolMaduro: { minLevel: 100, maxLevel: 199, imageUrl: 'trees/roble/arbolMaduro.png' },
      arbolFlorecido: { minLevel: 200, maxLevel: Infinity, imageUrl: 'trees/roble/arbolFlorecido.png' }
    }
  },
  cerezo: {
    name: 'Cerezo',
    description: 'Representa la belleza efímera y la renovación. Simboliza el amor que florece y se renueva constantemente.',
    spiritualMeaning: 'El cerezo simboliza la belleza del momento presente y la importancia de valorar cada día juntos.',
    stages: {
      semilla: { minLevel: 0, maxLevel: 6, imageUrl: 'trees/cerezo/semilla.png' },
      brote: { minLevel: 7, maxLevel: 20, imageUrl: 'trees/cerezo/brote.png' },
      plantula: { minLevel: 21, maxLevel: 49, imageUrl: 'trees/cerezo/plantula.png' },
      arbolJoven: { minLevel: 50, maxLevel: 99, imageUrl: 'trees/cerezo/arbolJoven.png' },
      arbolMaduro: { minLevel: 100, maxLevel: 199, imageUrl: 'trees/cerezo/arbolMaduro.png' },
      arbolFlorecido: { minLevel: 200, maxLevel: Infinity, imageUrl: 'trees/cerezo/arbolFlorecido.png' }
    }
  },
  pino: {
    name: 'Pino',
    description: 'Símbolo de resistencia y perseverancia. Representa una relación que permanece verde en todas las estaciones.',
    spiritualMeaning: 'El pino simboliza la constancia, la fidelidad y la capacidad de superar cualquier adversidad juntos.',
    stages: {
      semilla: { minLevel: 0, maxLevel: 6, imageUrl: 'trees/pino/semilla.png' },
      brote: { minLevel: 7, maxLevel: 20, imageUrl: 'trees/pino/brote.png' },
      plantula: { minLevel: 21, maxLevel: 49, imageUrl: 'trees/pino/plantula.png' },
      arbolJoven: { minLevel: 50, maxLevel: 99, imageUrl: 'trees/pino/arbolJoven.png' },
      arbolMaduro: { minLevel: 100, maxLevel: 199, imageUrl: 'trees/pino/arbolMaduro.png' },
      arbolFlorecido: { minLevel: 200, maxLevel: Infinity, imageUrl: 'trees/pino/arbolFlorecido.png' }
    }
  },
  sauce: {
    name: 'Sauce',
    description: 'Representa la flexibilidad y la adaptación. Simboliza una relación que se adapta y fluye con los cambios.',
    spiritualMeaning: 'El sauce simboliza la gracia, la intuición y la capacidad de encontrar paz en los momentos difíciles.',
    stages: {
      semilla: { minLevel: 0, maxLevel: 6, imageUrl: 'trees/sauce/semilla.png' },
      brote: { minLevel: 7, maxLevel: 20, imageUrl: 'trees/sauce/brote.png' },
      plantula: { minLevel: 21, maxLevel: 49, imageUrl: 'trees/sauce/plantula.png' },
      arbolJoven: { minLevel: 50, maxLevel: 99, imageUrl: 'trees/sauce/arbolJoven.png' },
      arbolMaduro: { minLevel: 100, maxLevel: 199, imageUrl: 'trees/sauce/arbolMaduro.png' },
      arbolFlorecido: { minLevel: 200, maxLevel: Infinity, imageUrl: 'trees/sauce/arbolFlorecido.png' }
    }
  },
  olivo: {
    name: 'Olivo',
    description: 'Símbolo de paz y longevidad. Representa una relación madura que da frutos duraderos.',
    spiritualMeaning: 'El olivo simboliza la paz, la abundancia y la sabiduría que viene con el tiempo compartido.',
    stages: {
      semilla: { minLevel: 0, maxLevel: 6, imageUrl: 'trees/olivo/semilla.png' },
      brote: { minLevel: 7, maxLevel: 20, imageUrl: 'trees/olivo/brote.png' },
      plantula: { minLevel: 21, maxLevel: 49, imageUrl: 'trees/olivo/plantula.png' },
      arbolJoven: { minLevel: 50, maxLevel: 99, imageUrl: 'trees/olivo/arbolJoven.png' },
      arbolMaduro: { minLevel: 100, maxLevel: 199, imageUrl: 'trees/olivo/arbolMaduro.png' },
      arbolFlorecido: { minLevel: 200, maxLevel: Infinity, imageUrl: 'trees/olivo/arbolFlorecido.png' }
    }
  }
};

const EXPERIENCE_PER_WATERING = 25;
const WATERING_COOLDOWN_HOURS = 24;

exports.handler = async (event, context) => {
  console.log('Tree service event:', JSON.stringify(event, null, 2));

  try {
    const { httpMethod, path, pathParameters } = event;
    const body = event.body ? JSON.parse(event.body) : {};

    // Handle CORS preflight
    if (httpMethod === 'OPTIONS') {
      return response.corsResponse();
    }

    // Extract user from token
    const user = auth.extractUserFromToken(event);
    if (!user) {
      return response.unauthorized();
    }

    // Route to appropriate handler
    const route = `${httpMethod} ${path}`;
    
    switch (route) {
      case 'GET /trees/types':
        return await handleGetTreeTypes();
      
      case 'POST /trees/select':
        return await handleSelectTreeType(user, body);
      
      case 'GET /trees/progress':
        return await handleGetTreeProgress(user, pathParameters);
      
      case 'POST /trees/water':
        return await handleWaterTree(user, pathParameters, body);
      
      case 'GET /trees/history':
        return await handleGetWateringHistory(user, pathParameters);
      
      case 'GET /trees/can-water':
        return await handleCanWaterToday(user, pathParameters);
      
      default:
        return response.notFound('Endpoint not found');
    }
  } catch (error) {
    console.error('Tree service error:', error);
    return response.handleError(error);
  }
};

/**
 * Get available tree types
 */
async function handleGetTreeTypes() {
  try {
    return response.success(TREE_TYPES);
  } catch (error) {
    console.error('Get tree types error:', error);
    return response.handleError(error);
  }
}

/**
 * Select tree type for couple
 */
async function handleSelectTreeType(user, body) {
  // Validate input
  const validation = validateBody(body, schemas.tree.selectType);
  if (!validation.isValid) {
    return response.validationError(validation.errors);
  }

  const { treeType } = validation.value;

  try {
    // Get user's couple
    const userProfile = await database.get(`USER#${user.userId}`, 'PROFILE');
    if (!userProfile || !userProfile.coupleId) {
      return response.validationError([
        { message: 'User must be part of a couple to select a tree' }
      ]);
    }

    const coupleId = userProfile.coupleId;

    // Check if tree type is valid
    if (!TREE_TYPES[treeType]) {
      return response.validationError([
        { field: 'treeType', message: 'Invalid tree type' }
      ]);
    }

    // Update couple's tree type and initialize progress
    const coupleUpdate = await database.update(
      `COUPLE#${coupleId}`,
      'METADATA',
      'SET treeType = :treeType, currentStage = :stage, #level = :level, experiencePoints = :exp',
      {
        ':treeType': treeType,
        ':stage': 'semilla',
        ':level': 0,
        ':exp': 0
      },
      {
        '#level': 'level'
      }
    );

    // Create tree progress record
    const treeProgress = {
      PK: `COUPLE#${coupleId}`,
      SK: 'TREE#PROGRESS',
      treeType,
      level: 0,
      currentStage: 'semilla',
      experiencePoints: 0,
      totalWaterings: 0,
      lastWatered: null,
      stageChangedAt: new Date().toISOString(),
      GSI1PK: 'TYPE#TREE_PROGRESS',
      GSI1SK: `COUPLE#${coupleId}`
    };

    await database.put(treeProgress);

    return response.success({
      coupleId,
      treeType,
      treeInfo: TREE_TYPES[treeType],
      progress: {
        level: 0,
        currentStage: 'semilla',
        experiencePoints: 0,
        totalWaterings: 0
      }
    });
  } catch (error) {
    console.error('Select tree type error:', error);
    return response.handleError(error);
  }
}

/**
 * Get tree progress for couple
 */
async function handleGetTreeProgress(user, pathParameters) {
  try {
    // Get user's couple
    const userProfile = await database.get(`USER#${user.userId}`, 'PROFILE');
    if (!userProfile || !userProfile.coupleId) {
      return response.notFound('User is not part of a couple');
    }

    const coupleId = userProfile.coupleId;

    // Get tree progress
    const treeProgress = await database.get(`COUPLE#${coupleId}`, 'TREE#PROGRESS');
    if (!treeProgress) {
      return response.notFound('Tree not found. Please select a tree type first.');
    }

    // Get tree type information
    const treeInfo = TREE_TYPES[treeProgress.treeType];
    if (!treeInfo) {
      return response.error('Invalid tree type in database');
    }

    // Calculate current stage based on level
    const currentStage = calculateTreeStage(treeProgress.level, treeInfo.stages);
    
    // Check if user can water today
    const canWater = await checkCanWaterToday(user.userId, coupleId);

    return response.success({
      coupleId,
      treeType: treeProgress.treeType,
      treeInfo,
      progress: {
        level: treeProgress.level,
        currentStage,
        experiencePoints: treeProgress.experiencePoints,
        totalWaterings: treeProgress.totalWaterings,
        lastWatered: treeProgress.lastWatered,
        stageChangedAt: treeProgress.stageChangedAt,
        canWaterToday: canWater,
        nextStageAt: getNextStageLevel(currentStage, treeInfo.stages)
      }
    });
  } catch (error) {
    console.error('Get tree progress error:', error);
    return response.handleError(error);
  }
}

/**
 * Water the tree (daily action)
 */
async function handleWaterTree(user, pathParameters, body) {
  try {
    // Get user's couple
    const userProfile = await database.get(`USER#${user.userId}`, 'PROFILE');
    if (!userProfile || !userProfile.coupleId) {
      return response.notFound('User is not part of a couple');
    }

    const coupleId = userProfile.coupleId;

    // Check if user can water today
    const canWater = await checkCanWaterToday(user.userId, coupleId);
    if (!canWater) {
      return response.conflict('You have already watered the tree today. Come back tomorrow!');
    }

    // Get current tree progress
    const treeProgress = await database.get(`COUPLE#${coupleId}`, 'TREE#PROGRESS');
    if (!treeProgress) {
      return response.notFound('Tree not found. Please select a tree type first.');
    }

    const today = new Date().toISOString().split('T')[0];
    const wateringId = uuidv4();
    
    // Calculate new experience and level
    const newExperience = treeProgress.experiencePoints + EXPERIENCE_PER_WATERING;
    const newLevel = Math.floor(newExperience / EXPERIENCE_PER_WATERING);
    const oldStage = treeProgress.currentStage;
    
    // Get tree info and calculate new stage
    const treeInfo = TREE_TYPES[treeProgress.treeType];
    const newStage = calculateTreeStage(newLevel, treeInfo.stages);
    const stageChanged = oldStage !== newStage;

    // Create watering record
    const wateringRecord = {
      PK: `COUPLE#${coupleId}`,
      SK: `WATERING#${today}#${user.userId}`,
      wateringId,
      userId: user.userId,
      date: today,
      timestamp: new Date().toISOString(),
      experienceGained: EXPERIENCE_PER_WATERING,
      levelBefore: treeProgress.level,
      levelAfter: newLevel,
      stageChanged,
      message: body.message || null,
      GSI1PK: 'TYPE#WATERING',
      GSI1SK: `DATE#${today}#${coupleId}`
    };

    // Update tree progress
    const updateExpression = stageChanged 
      ? 'SET experiencePoints = :exp, #level = :level, currentStage = :stage, totalWaterings = totalWaterings + :inc, lastWatered = :date, stageChangedAt = :stageChanged'
      : 'SET experiencePoints = :exp, #level = :level, totalWaterings = totalWaterings + :inc, lastWatered = :date';
    
    const expressionValues = {
      ':exp': newExperience,
      ':level': newLevel,
      ':inc': 1,
      ':date': today
    };

    if (stageChanged) {
      expressionValues[':stage'] = newStage;
      expressionValues[':stageChanged'] = new Date().toISOString();
    }

    // Use transaction to ensure consistency
    const transactItems = [
      {
        Put: {
          TableName: process.env.DYNAMODB_TABLE,
          Item: wateringRecord
        }
      },
      {
        Update: {
          TableName: process.env.DYNAMODB_TABLE,
          Key: {
            PK: `COUPLE#${coupleId}`,
            SK: 'TREE#PROGRESS'
          },
          UpdateExpression: updateExpression,
          ExpressionAttributeValues: expressionValues,
          ExpressionAttributeNames: {
            '#level': 'level'
          }
        }
      }
    ];

    await database.transactWrite(transactItems);

    // If stage changed, create an event for notifications
    if (stageChanged) {
      const stageChangeEvent = {
        PK: `EVENT#${Date.now()}`,
        SK: 'TREE_STAGE_CHANGE',
        eventType: 'TREE_STAGE_CHANGE',
        coupleId,
        userId: user.userId,
        oldStage,
        newStage,
        newLevel,
        timestamp: new Date().toISOString(),
        GSI1PK: 'TYPE#EVENT',
        GSI1SK: `TREE_STAGE_CHANGE#${coupleId}`,
        TTL: Math.floor(Date.now() / 1000) + (7 * 24 * 60 * 60) // 7 days
      };

      await database.put(stageChangeEvent);
    }

    return response.success({
      watered: true,
      experienceGained: EXPERIENCE_PER_WATERING,
      newLevel,
      newStage,
      stageChanged,
      totalExperience: newExperience,
      nextWateringAvailable: getNextWateringTime(),
      message: stageChanged 
        ? `¡Felicidades! Su árbol ha crecido a la etapa: ${newStage}` 
        : '¡Árbol regado exitosamente!'
    });
  } catch (error) {
    console.error('Water tree error:', error);
    return response.handleError(error);
  }
}

/**
 * Get watering history for couple
 */
async function handleGetWateringHistory(user, pathParameters) {
  try {
    // Get user's couple
    const userProfile = await database.get(`USER#${user.userId}`, 'PROFILE');
    if (!userProfile || !userProfile.coupleId) {
      return response.notFound('User is not part of a couple');
    }

    const coupleId = userProfile.coupleId;

    // Query watering history
    const wateringHistory = await database.query(
      `COUPLE#${coupleId}`,
      {
        expression: 'begins_with(SK, :sk)',
        values: { ':sk': 'WATERING#' }
      },
      {
        ScanIndexForward: false, // Most recent first
        Limit: 30 // Last 30 waterings
      }
    );

    return response.success({
      coupleId,
      history: wateringHistory.items,
      total: wateringHistory.count
    });
  } catch (error) {
    console.error('Get watering history error:', error);
    return response.handleError(error);
  }
}

/**
 * Check if user can water today
 */
async function handleCanWaterToday(user, pathParameters) {
  try {
    // Get user's couple
    const userProfile = await database.get(`USER#${user.userId}`, 'PROFILE');
    if (!userProfile || !userProfile.coupleId) {
      return response.notFound('User is not part of a couple');
    }

    const coupleId = userProfile.coupleId;
    const canWater = await checkCanWaterToday(user.userId, coupleId);

    return response.success({
      canWater,
      nextWateringAvailable: canWater ? null : getNextWateringTime()
    });
  } catch (error) {
    console.error('Check can water error:', error);
    return response.handleError(error);
  }
}

// Helper functions

function calculateTreeStage(level, stages) {
  for (const [stageName, stageInfo] of Object.entries(stages)) {
    if (level >= stageInfo.minLevel && level <= stageInfo.maxLevel) {
      return stageName;
    }
  }
  return 'semilla'; // Default to first stage
}

function getNextStageLevel(currentStage, stages) {
  const stageNames = Object.keys(stages);
  const currentIndex = stageNames.indexOf(currentStage);
  
  if (currentIndex < stageNames.length - 1) {
    const nextStage = stageNames[currentIndex + 1];
    return stages[nextStage].minLevel;
  }
  
  return null; // Already at max stage
}

async function checkCanWaterToday(userId, coupleId) {
  const today = new Date().toISOString().split('T')[0];
  
  try {
    const todayWatering = await database.get(
      `COUPLE#${coupleId}`,
      `WATERING#${today}#${userId}`
    );
    
    return !todayWatering; // Can water if no record exists for today
  } catch (error) {
    console.error('Check watering error:', error);
    return false;
  }
}

function getNextWateringTime() {
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);
  tomorrow.setHours(0, 0, 0, 0);
  return tomorrow.toISOString();
}
