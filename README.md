# 🌳 HopieApp - Serverless Backend

A complete serverless backend for HopieApp, a couples relationship app that helps partners grow their relationship through daily activities, questions, and shared experiences.

## 🏗️ Architecture

This project uses AWS SAM (Serverless Application Model) to deploy a complete serverless architecture including:

- **12 Lambda Functions** - Microservices for different app features
- **DynamoDB** - Single table design for all data storage
- **API Gateway** - REST APIs and WebSocket for real-time chat
- **Cognito** - User authentication and management
- **S3 + CloudFront** - Image storage and CDN
- **EventBridge** - Scheduled tasks for daily questions and streaks

## 🚀 Quick Start

### Prerequisites

- [AWS CLI](https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html) configured
- [SAM CLI](https://docs.aws.amazon.com/serverless-application-model/latest/developerguide/serverless-sam-cli-install.html) installed
- [Node.js 18+](https://nodejs.org/) installed
- [Docker](https://www.docker.com/) for local development

### Deploy to AWS

1. **Clone and install dependencies:**
   ```bash
   git clone <repository-url>
   cd hopie-app-backend
   npm install
   ```

2. **Deploy to development environment:**
   ```bash
   chmod +x scripts/deploy.sh
   ./scripts/deploy.sh dev
   ```

3. **Deploy to production:**
   ```bash
   ./scripts/deploy.sh prod
   ```

### Local Development

1. **Set up local environment:**
   ```bash
   chmod +x scripts/local-dev.sh
   ./scripts/local-dev.sh
   ```

2. **Start local API server:**
   ```bash
   sam local start-api --port 3000 --env-vars .env.local
   ```

## 📊 Database Schema

### Single Table Design (DynamoDB)

The application uses a single DynamoDB table with the following access patterns:

```
PK: ENTITY#{type}#{id}
SK: METADATA | RELATION#{type}#{id} | TIMESTAMP#{date}

Examples:
- USER#123, PROFILE
- COUPLE#456, METADATA
- COUPLE#456, TREE#PROGRESS
- COUPLE#456, MESSAGE#2024-01-15#001
```

### Global Secondary Indexes

- **GSI1**: `GSI1PK` (TYPE#{entityType}) + `GSI1SK` (CREATED#{timestamp})
- **GSI2**: `GSI2PK` (STATUS#{status}) + `GSI2SK` (DATE#{date})

## 🔧 Services

### Core Services

| Service | Description | Endpoints |
|---------|-------------|-----------|
| **Auth** | User authentication | `/auth/*` |
| **User** | User profile management | `/users/*` |
| **Couple** | Couple formation & management | `/couples/*` |
| **Tree** | Tree growth & watering | `/trees/*` |
| **Questions** | Daily questions & answers | `/questions/*` |
| **Chat** | Real-time messaging | WebSocket `/chat` |

### Feature Services

| Service | Description | Endpoints |
|---------|-------------|-----------|
| **Places** | Special places management | `/places/*` |
| **LifePlan** | Life plan stages & goals | `/lifeplan/*` |
| **Location** | Location sharing | `/location/*` |
| **Stats** | Statistics & analytics | `/stats/*` |
| **Image** | Image upload & processing | `/images/*` |
| **Notification** | Push notifications | `/notifications/*` |

## 🌐 API Documentation

### Authentication

All endpoints (except auth) require a valid JWT token in the Authorization header:

```
Authorization: Bearer <jwt-token>
```

### Core Endpoints

#### Authentication
```http
POST /auth/register
POST /auth/login
GET /auth/profile
PUT /auth/profile
POST /auth/logout
```

#### Tree Management
```http
GET /trees/types
POST /trees/select
GET /trees/progress
POST /trees/water
GET /trees/history
```

#### Daily Questions
```http
GET /questions/today
POST /questions/answer
GET /questions/history
GET /questions/couple-answers/{date}
```

#### Real-time Chat
```javascript
// WebSocket connection
const ws = new WebSocket('wss://your-websocket-url/chat?token=jwt-token');

// Send message
ws.send(JSON.stringify({
  action: 'sendMessage',
  data: {
    content: 'Hello!',
    type: 'text'
  }
}));
```

## 🔐 Environment Configuration

### Development
```json
{
  "environment": "dev",
  "apiUrl": "https://api-dev.hopie-app.com",
  "webSocketUrl": "wss://ws-dev.hopie-app.com",
  "cloudFrontUrl": "https://cdn-dev.hopie-app.com",
  "cognito": {
    "userPoolId": "us-east-1_xxxxxxxxx",
    "userPoolClientId": "xxxxxxxxxxxxxxxxxxxxxxxxxx",
    "region": "us-east-1"
  }
}
```

## 🧪 Testing

Run tests with the provided script:

```bash
# Run all tests
./scripts/test.sh

# Run specific test types
./scripts/test.sh unit
./scripts/test.sh integration
./scripts/test.sh e2e
```

## 📦 Deployment

### Environments

- **dev** - Development environment
- **staging** - Staging environment  
- **prod** - Production environment

### Deployment Commands

```bash
# Deploy to specific environment
./scripts/deploy.sh [dev|staging|prod]

# Build only
sam build

# Validate template
sam validate

# Local testing
sam local start-api
```

## 🗄️ Data Models

### User
```json
{
  "PK": "USER#123",
  "SK": "PROFILE",
  "userId": "123",
  "email": "<EMAIL>",
  "name": "John Doe",
  "coupleId": "456",
  "partnerId": "124"
}
```

### Couple
```json
{
  "PK": "COUPLE#456",
  "SK": "METADATA",
  "coupleId": "456",
  "user1Id": "123",
  "user2Id": "124",
  "level": 15,
  "treeType": "roble",
  "currentStage": "plantula"
}
```

### Tree Progress
```json
{
  "PK": "COUPLE#456",
  "SK": "TREE#PROGRESS",
  "level": 15,
  "currentStage": "plantula",
  "experiencePoints": 375,
  "totalWaterings": 15,
  "lastWatered": "2024-01-15"
}
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `STAGE` | Deployment stage | `dev`, `staging`, `prod` |
| `DYNAMODB_TABLE` | DynamoDB table name | `HopieApp-dev` |
| `USER_POOL_ID` | Cognito User Pool ID | `us-east-1_xxxxxxxxx` |
| `USER_POOL_CLIENT_ID` | Cognito Client ID | `xxxxxxxxxxxxxxxxxxxxxxxxxx` |
| `USER_CONTENT_BUCKET` | S3 bucket for user content | `hopie-user-content-dev` |
| `APP_ASSETS_BUCKET` | S3 bucket for app assets | `hopie-app-assets-dev` |

## 🚨 Monitoring & Logging

### CloudWatch Logs

Each Lambda function logs to CloudWatch with structured logging:

```bash
# View logs for specific function
sam logs -n AuthFunction --stack-name hopie-app-dev --tail
```

### Metrics

Key metrics to monitor:
- Lambda invocations and errors
- DynamoDB read/write capacity
- API Gateway request count and latency
- WebSocket connections

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run the test suite
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check the [API Documentation](docs/api.md)
- Review the [Architecture Documentation](BACKEND_ARCHITECTURE.md)
