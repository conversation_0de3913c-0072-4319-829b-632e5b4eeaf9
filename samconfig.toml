# SAM Configuration File
version = 0.1

[default]
[default.global]
[default.global.parameters]
stack_name = "hopie-app"

[default.build]
[default.build.parameters]
cached = true
parallel = true

[default.validate]
[default.validate.parameters]
lint = true

[default.deploy]
[default.deploy.parameters]
capabilities = "CAPABILITY_IAM"
confirm_changeset = true
resolve_s3 = true
s3_prefix = "hopie-app"
region = "us-east-1"
image_repositories = []

[dev]
[dev.global]
[dev.global.parameters]
stack_name = "hopie-app-dev"

[dev.deploy]
[dev.deploy.parameters]
capabilities = "CAPABILITY_IAM"
confirm_changeset = false
resolve_s3 = true
s3_prefix = "hopie-app-dev"
region = "us-east-1"
parameter_overrides = [
    "Stage=dev",
    "CognitoDomainPrefix=hopie-app-dev"
]

[staging]
[staging.global]
[staging.global.parameters]
stack_name = "hopie-app-staging"

[staging.deploy]
[staging.deploy.parameters]
capabilities = "CAPABILITY_IAM"
confirm_changeset = true
resolve_s3 = true
s3_prefix = "hopie-app-staging"
region = "us-east-1"
parameter_overrides = [
    "Stage=staging",
    "CognitoDomainPrefix=hopie-app-staging"
]

[prod]
[prod.global]
[prod.global.parameters]
stack_name = "hopie-app-prod"

[prod.deploy]
[prod.deploy.parameters]
capabilities = "CAPABILITY_IAM"
confirm_changeset = true
resolve_s3 = true
s3_prefix = "hopie-app-prod"
region = "us-east-1"
parameter_overrides = [
    "Stage=prod",
    "CognitoDomainPrefix=hopie-app-prod"
]
