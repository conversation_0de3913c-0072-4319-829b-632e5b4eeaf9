const AWS = require('aws-sdk');
const fs = require('fs');
const path = require('path');

/**
 * Seed initial data for HopieApp
 * Usage: node scripts/seed-data.js [environment]
 */

const environment = process.argv[2] || 'dev';
const configPath = path.join(__dirname, '..', 'config', `${environment}.json`);

// Load environment configuration
if (!fs.existsSync(configPath)) {
  console.error(`❌ Configuration file not found: ${configPath}`);
  process.exit(1);
}

const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));

// Configure AWS
AWS.config.update({ region: 'us-east-1' });
const dynamodb = new AWS.DynamoDB.DocumentClient();
const s3 = new AWS.S3();

const TABLE_NAME = `HopieApp-${environment}`;
const APP_ASSETS_BUCKET = `hopie-app-assets-${environment}-${process.env.AWS_ACCOUNT_ID || '************'}`;

async function seedData() {
  console.log(`🌱 Seeding data for ${environment} environment...`);

  try {
    // Seed tree types
    await seedTreeTypes();
    
    // Seed sample questions
    await seedSampleQuestions();
    
    // Seed app assets metadata
    await seedAppAssets();
    
    console.log('✅ Data seeding completed successfully!');
  } catch (error) {
    console.error('❌ Error seeding data:', error);
    process.exit(1);
  }
}

/**
 * Seed tree types data
 */
async function seedTreeTypes() {
  console.log('📊 Seeding tree types...');

  const treeTypes = [
    {
      PK: 'TREE_TYPE#roble',
      SK: 'METADATA',
      treeType: 'roble',
      name: 'Roble',
      description: 'Símbolo de fortaleza y resistencia. Representa una relación sólida que crece lentamente pero con raíces profundas.',
      spiritualMeaning: 'El roble simboliza la sabiduría, la protección y la longevidad en el amor.',
      stages: {
        semilla: { minLevel: 0, maxLevel: 6, imageUrl: 'trees/roble/semilla.png' },
        brote: { minLevel: 7, maxLevel: 20, imageUrl: 'trees/roble/brote.png' },
        plantula: { minLevel: 21, maxLevel: 49, imageUrl: 'trees/roble/plantula.png' },
        arbolJoven: { minLevel: 50, maxLevel: 99, imageUrl: 'trees/roble/arbolJoven.png' },
        arbolMaduro: { minLevel: 100, maxLevel: 199, imageUrl: 'trees/roble/arbolMaduro.png' },
        arbolFlorecido: { minLevel: 200, maxLevel: Infinity, imageUrl: 'trees/roble/arbolFlorecido.png' }
      },
      GSI1PK: 'TYPE#TREE_TYPE',
      GSI1SK: 'TREE_TYPE#roble',
      createdAt: new Date().toISOString()
    },
    {
      PK: 'TREE_TYPE#cerezo',
      SK: 'METADATA',
      treeType: 'cerezo',
      name: 'Cerezo',
      description: 'Representa la belleza efímera y la renovación. Simboliza el amor que florece y se renueva constantemente.',
      spiritualMeaning: 'El cerezo simboliza la belleza del momento presente y la importancia de valorar cada día juntos.',
      stages: {
        semilla: { minLevel: 0, maxLevel: 6, imageUrl: 'trees/cerezo/semilla.png' },
        brote: { minLevel: 7, maxLevel: 20, imageUrl: 'trees/cerezo/brote.png' },
        plantula: { minLevel: 21, maxLevel: 49, imageUrl: 'trees/cerezo/plantula.png' },
        arbolJoven: { minLevel: 50, maxLevel: 99, imageUrl: 'trees/cerezo/arbolJoven.png' },
        arbolMaduro: { minLevel: 100, maxLevel: 199, imageUrl: 'trees/cerezo/arbolMaduro.png' },
        arbolFlorecido: { minLevel: 200, maxLevel: Infinity, imageUrl: 'trees/cerezo/arbolFlorecido.png' }
      },
      GSI1PK: 'TYPE#TREE_TYPE',
      GSI1SK: 'TREE_TYPE#cerezo',
      createdAt: new Date().toISOString()
    },
    {
      PK: 'TREE_TYPE#pino',
      SK: 'METADATA',
      treeType: 'pino',
      name: 'Pino',
      description: 'Símbolo de resistencia y perseverancia. Representa una relación que permanece verde en todas las estaciones.',
      spiritualMeaning: 'El pino simboliza la constancia, la fidelidad y la capacidad de superar cualquier adversidad juntos.',
      stages: {
        semilla: { minLevel: 0, maxLevel: 6, imageUrl: 'trees/pino/semilla.png' },
        brote: { minLevel: 7, maxLevel: 20, imageUrl: 'trees/pino/brote.png' },
        plantula: { minLevel: 21, maxLevel: 49, imageUrl: 'trees/pino/plantula.png' },
        arbolJoven: { minLevel: 50, maxLevel: 99, imageUrl: 'trees/pino/arbolJoven.png' },
        arbolMaduro: { minLevel: 100, maxLevel: 199, imageUrl: 'trees/pino/arbolMaduro.png' },
        arbolFlorecido: { minLevel: 200, maxLevel: Infinity, imageUrl: 'trees/pino/arbolFlorecido.png' }
      },
      GSI1PK: 'TYPE#TREE_TYPE',
      GSI1SK: 'TREE_TYPE#pino',
      createdAt: new Date().toISOString()
    },
    {
      PK: 'TREE_TYPE#sauce',
      SK: 'METADATA',
      treeType: 'sauce',
      name: 'Sauce',
      description: 'Representa la flexibilidad y la adaptación. Simboliza una relación que se adapta y fluye con los cambios.',
      spiritualMeaning: 'El sauce simboliza la gracia, la intuición y la capacidad de encontrar paz en los momentos difíciles.',
      stages: {
        semilla: { minLevel: 0, maxLevel: 6, imageUrl: 'trees/sauce/semilla.png' },
        brote: { minLevel: 7, maxLevel: 20, imageUrl: 'trees/sauce/brote.png' },
        plantula: { minLevel: 21, maxLevel: 49, imageUrl: 'trees/sauce/plantula.png' },
        arbolJoven: { minLevel: 50, maxLevel: 99, imageUrl: 'trees/sauce/arbolJoven.png' },
        arbolMaduro: { minLevel: 100, maxLevel: 199, imageUrl: 'trees/sauce/arbolMaduro.png' },
        arbolFlorecido: { minLevel: 200, maxLevel: Infinity, imageUrl: 'trees/sauce/arbolFlorecido.png' }
      },
      GSI1PK: 'TYPE#TREE_TYPE',
      GSI1SK: 'TREE_TYPE#sauce',
      createdAt: new Date().toISOString()
    },
    {
      PK: 'TREE_TYPE#olivo',
      SK: 'METADATA',
      treeType: 'olivo',
      name: 'Olivo',
      description: 'Símbolo de paz y longevidad. Representa una relación madura que da frutos duraderos.',
      spiritualMeaning: 'El olivo simboliza la paz, la abundancia y la sabiduría que viene con el tiempo compartido.',
      stages: {
        semilla: { minLevel: 0, maxLevel: 6, imageUrl: 'trees/olivo/semilla.png' },
        brote: { minLevel: 7, maxLevel: 20, imageUrl: 'trees/olivo/brote.png' },
        plantula: { minLevel: 21, maxLevel: 49, imageUrl: 'trees/olivo/plantula.png' },
        arbolJoven: { minLevel: 50, maxLevel: 99, imageUrl: 'trees/olivo/arbolJoven.png' },
        arbolMaduro: { minLevel: 100, maxLevel: 199, imageUrl: 'trees/olivo/arbolMaduro.png' },
        arbolFlorecido: { minLevel: 200, maxLevel: Infinity, imageUrl: 'trees/olivo/arbolFlorecido.png' }
      },
      GSI1PK: 'TYPE#TREE_TYPE',
      GSI1SK: 'TREE_TYPE#olivo',
      createdAt: new Date().toISOString()
    }
  ];

  // Batch write tree types
  const chunks = chunkArray(treeTypes, 25); // DynamoDB batch write limit
  
  for (const chunk of chunks) {
    const putRequests = chunk.map(item => ({
      PutRequest: { Item: item }
    }));

    await dynamodb.batchWrite({
      RequestItems: {
        [TABLE_NAME]: putRequests
      }
    }).promise();
  }

  console.log(`✅ Seeded ${treeTypes.length} tree types`);
}

/**
 * Seed sample daily questions
 */
async function seedSampleQuestions() {
  console.log('❓ Seeding sample questions...');

  const today = new Date();
  const questions = [];

  // Create questions for the next 7 days
  for (let i = 0; i < 7; i++) {
    const date = new Date(today);
    date.setDate(date.getDate() + i);
    const dateStr = date.toISOString().split('T')[0];

    const sampleQuestions = [
      { category: 'memories', question: '¿Cuál es tu recuerdo favorito de nosotros?' },
      { category: 'dreams', question: '¿Cómo te imaginas nuestro futuro en 5 años?' },
      { category: 'feelings', question: '¿Qué es lo que más admiras de mí?' },
      { category: 'growth', question: '¿En qué hemos crecido más como pareja?' },
      { category: 'fun', question: '¿Cuál es la actividad más divertida que hacemos juntos?' },
      { category: 'memories', question: '¿Qué momento juntos te hace sonreír siempre?' },
      { category: 'dreams', question: '¿Qué sueño te gustaría que cumplamos juntos?' }
    ];

    const questionData = sampleQuestions[i % sampleQuestions.length];

    questions.push({
      PK: `DAILY_QUESTION#${dateStr}`,
      SK: 'QUESTION',
      date: dateStr,
      question: questionData.question,
      category: questionData.category,
      createdAt: new Date().toISOString(),
      GSI1PK: 'TYPE#DAILY_QUESTION',
      GSI1SK: `DATE#${dateStr}`
    });
  }

  // Batch write questions
  const chunks = chunkArray(questions, 25);
  
  for (const chunk of chunks) {
    const putRequests = chunk.map(item => ({
      PutRequest: { Item: item }
    }));

    await dynamodb.batchWrite({
      RequestItems: {
        [TABLE_NAME]: putRequests
      }
    }).promise();
  }

  console.log(`✅ Seeded ${questions.length} sample questions`);
}

/**
 * Seed app assets metadata
 */
async function seedAppAssets() {
  console.log('🎨 Seeding app assets metadata...');

  const assets = [
    {
      PK: 'APP_ASSET#backgrounds',
      SK: 'METADATA',
      assetType: 'backgrounds',
      name: 'Tree Backgrounds',
      description: 'Background images for tree visualization',
      assets: {
        forest: 'backgrounds/tree-backgrounds/forest.jpg',
        garden: 'backgrounds/tree-backgrounds/garden.jpg',
        sunset: 'backgrounds/tree-backgrounds/sunset.jpg'
      },
      GSI1PK: 'TYPE#APP_ASSET',
      GSI1SK: 'ASSET#backgrounds',
      createdAt: new Date().toISOString()
    },
    {
      PK: 'APP_ASSET#icons',
      SK: 'METADATA',
      assetType: 'icons',
      name: 'Place Category Icons',
      description: 'Icons for different place categories',
      assets: {
        restaurant: 'icons/place-categories/restaurant.svg',
        cafe: 'icons/place-categories/cafe.svg',
        park: 'icons/place-categories/park.svg',
        home: 'icons/place-categories/home.svg',
        beach: 'icons/place-categories/beach.svg',
        mountain: 'icons/place-categories/mountain.svg'
      },
      GSI1PK: 'TYPE#APP_ASSET',
      GSI1SK: 'ASSET#icons',
      createdAt: new Date().toISOString()
    }
  ];

  // Batch write assets
  const putRequests = assets.map(item => ({
    PutRequest: { Item: item }
  }));

  await dynamodb.batchWrite({
    RequestItems: {
      [TABLE_NAME]: putRequests
    }
  }).promise();

  console.log(`✅ Seeded ${assets.length} app asset records`);
}

/**
 * Utility function to chunk array
 */
function chunkArray(array, chunkSize) {
  const chunks = [];
  for (let i = 0; i < array.length; i += chunkSize) {
    chunks.push(array.slice(i, i + chunkSize));
  }
  return chunks;
}

// Run the seeding
seedData();
