# 🌐 HopieApp API Documentation

Complete API reference for the HopieApp backend services.

## 🔐 Authentication

All API endpoints (except authentication endpoints) require a valid JWT token in the Authorization header.

```http
Authorization: Bearer <jwt-token>
```

## 📋 Base URLs

- **Development**: `https://api-dev.hopie-app.com`
- **Staging**: `https://api-staging.hopie-app.com`
- **Production**: `https://api.hopie-app.com`
- **WebSocket**: `wss://ws.hopie-app.com/chat`

## 🔑 Authentication Endpoints

### Register User
```http
POST /auth/register
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securePassword123",
  "name": "<PERSON>",
  "confirmPassword": "securePassword123"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "userId": "123e4567-e89b-12d3-a456-426614174000",
    "email": "<EMAIL>",
    "name": "<PERSON>",
    "confirmationRequired": true,
    "message": "User registered successfully. Please check your email for confirmation."
  }
}
```

### Login User
```http
POST /auth/login
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securePassword123"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "tokens": {
      "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "idToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "expiresIn": 3600
    },
    "user": {
      "userId": "123e4567-e89b-12d3-a456-426614174000",
      "email": "<EMAIL>",
      "name": "John Doe",
      "emailVerified": true
    }
  }
}
```

### Get User Profile
```http
GET /auth/profile
```

**Response:**
```json
{
  "success": true,
  "data": {
    "userId": "123e4567-e89b-12d3-a456-426614174000",
    "email": "<EMAIL>",
    "name": "John Doe",
    "avatarUrl": "https://cdn.hopie-app.com/avatars/user-123/profile.jpg",
    "coupleId": "456e7890-e89b-12d3-a456-426614174000",
    "partnerId": "789e0123-e89b-12d3-a456-426614174000",
    "settings": {
      "notifications": true,
      "locationSharing": false
    }
  }
}
```

## 👥 Couple Management

### Send Invitation
```http
POST /couples/invite
```

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

### Accept Invitation
```http
POST /couples/accept
```

**Request Body:**
```json
{
  "invitationId": "inv-123e4567-e89b-12d3-a456-426614174000"
}
```

### Get Couple Information
```http
GET /couples/info
```

**Response:**
```json
{
  "success": true,
  "data": {
    "couple": {
      "coupleId": "456e7890-e89b-12d3-a456-426614174000",
      "level": 15,
      "experiencePoints": 375,
      "treeType": "roble",
      "currentStage": "plantula",
      "formedAt": "2024-01-01T00:00:00Z"
    },
    "partner": {
      "userId": "789e0123-e89b-12d3-a456-426614174000",
      "name": "Jane Doe",
      "email": "<EMAIL>",
      "avatarUrl": "https://cdn.hopie-app.com/avatars/user-789/profile.jpg"
    },
    "treeProgress": {
      "level": 15,
      "currentStage": "plantula",
      "experiencePoints": 375,
      "totalWaterings": 15,
      "lastWatered": "2024-01-15"
    }
  }
}
```

## 🌳 Tree Management

### Get Tree Types
```http
GET /trees/types
```

**Response:**
```json
{
  "success": true,
  "data": {
    "roble": {
      "name": "Roble",
      "description": "Símbolo de fortaleza y resistencia...",
      "spiritualMeaning": "El roble simboliza la sabiduría...",
      "stages": {
        "semilla": {
          "minLevel": 0,
          "maxLevel": 6,
          "imageUrl": "trees/roble/semilla.png"
        }
      }
    }
  }
}
```

### Select Tree Type
```http
POST /trees/select
```

**Request Body:**
```json
{
  "treeType": "roble"
}
```

### Get Tree Progress
```http
GET /trees/progress
```

**Response:**
```json
{
  "success": true,
  "data": {
    "coupleId": "456e7890-e89b-12d3-a456-426614174000",
    "treeType": "roble",
    "treeInfo": {
      "name": "Roble",
      "description": "Símbolo de fortaleza y resistencia..."
    },
    "progress": {
      "level": 15,
      "currentStage": "plantula",
      "experiencePoints": 375,
      "totalWaterings": 15,
      "lastWatered": "2024-01-15",
      "canWaterToday": false,
      "nextStageAt": 21
    }
  }
}
```

### Water Tree
```http
POST /trees/water
```

**Request Body:**
```json
{
  "message": "¡Regando nuestro árbol con amor!"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "watered": true,
    "experienceGained": 25,
    "newLevel": 16,
    "newStage": "plantula",
    "stageChanged": false,
    "totalExperience": 400,
    "nextWateringAvailable": "2024-01-16T00:00:00Z",
    "message": "¡Árbol regado exitosamente!"
  }
}
```

## ❓ Daily Questions

### Get Today's Question
```http
GET /questions/today
```

**Response:**
```json
{
  "success": true,
  "data": {
    "question": {
      "date": "2024-01-15",
      "question": "¿Cuál es tu recuerdo favorito de nosotros?",
      "category": "memories"
    },
    "hasAnswered": false,
    "partnerHasAnswered": true,
    "userAnswer": null,
    "partnerAnswer": null,
    "bothAnswered": false
  }
}
```

### Answer Question
```http
POST /questions/answer
```

**Request Body:**
```json
{
  "questionId": "2024-01-15",
  "answer": "Nuestro primer viaje juntos a la playa fue increíble..."
}
```

### Get Question History
```http
GET /questions/history?limit=20
```

**Response:**
```json
{
  "success": true,
  "data": {
    "coupleId": "456e7890-e89b-12d3-a456-426614174000",
    "history": [
      {
        "questionId": "2024-01-15",
        "question": "¿Cuál es tu recuerdo favorito de nosotros?",
        "date": "2024-01-15",
        "answers": {
          "123e4567-e89b-12d3-a456-426614174000": {
            "userId": "123e4567-e89b-12d3-a456-426614174000",
            "answer": "Nuestro primer viaje...",
            "answeredAt": "2024-01-15T14:30:00Z"
          }
        }
      }
    ],
    "total": 15
  }
}
```

## 💬 Real-time Chat (WebSocket)

### Connection
```javascript
const ws = new WebSocket('wss://ws.hopie-app.com/chat?token=jwt-token');

ws.onopen = function() {
  console.log('Connected to chat');
};

ws.onmessage = function(event) {
  const data = JSON.parse(event.data);
  console.log('Received:', data);
};
```

### Send Message
```javascript
ws.send(JSON.stringify({
  action: 'sendMessage',
  data: {
    content: 'Hello, love!',
    type: 'text'
  }
}));
```

### Message Types
- `newMessage` - New message received
- `messageConfirmation` - Message sent confirmation
- `messagesRead` - Messages marked as read
- `partnerStatus` - Partner online/offline status
- `typing` - Typing indicator

## 📍 Places Management

### Get Places
```http
GET /places
```

### Create Place
```http
POST /places
```

**Request Body:**
```json
{
  "name": "Nuestro primer café",
  "description": "Donde tuvimos nuestra primera cita",
  "category": "cafe",
  "coordinates": {
    "latitude": 19.4326,
    "longitude": -99.1332
  },
  "address": "Calle Principal 123",
  "mainImageUrl": "https://cdn.hopie-app.com/couples/456/places/789/main.jpg"
}
```

## 📊 Life Plan Management

### Get Life Plan Stages
```http
GET /lifeplan/stages
```

### Create Life Plan Stage
```http
POST /lifeplan/stage
```

**Request Body:**
```json
{
  "title": "Mudarnos juntos",
  "description": "Encontrar un lugar para vivir juntos",
  "targetDate": "2024-06-01",
  "order": 1
}
```

## 📈 Statistics

### Get User Streak
```http
GET /stats/streak
```

**Response:**
```json
{
  "success": true,
  "data": {
    "streak": {
      "currentStreak": 15,
      "longestStreak": 25,
      "totalActiveDays": 45
    }
  }
}
```

### Get Dashboard Data
```http
GET /stats/dashboard
```

## 📸 Image Management

### Get Upload URL
```http
POST /images/upload-url
```

**Request Body:**
```json
{
  "fileName": "profile.jpg",
  "fileType": "image/jpeg",
  "category": "avatar"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "uploadUrl": "https://s3.amazonaws.com/bucket/path?signature=...",
    "filePath": "avatars/user-123/profile.jpg",
    "expiresIn": 300
  }
}
```

## 🔔 Notifications

### Get Notifications
```http
GET /notifications
```

### Register Device
```http
POST /notifications/register-device
```

**Request Body:**
```json
{
  "deviceToken": "fcm-device-token",
  "platform": "ios"
}
```

## ❌ Error Responses

All error responses follow this format:

```json
{
  "success": false,
  "error": {
    "message": "Error description",
    "code": "ERROR_CODE",
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

### Common Error Codes

- `UNAUTHORIZED` (401) - Invalid or missing authentication
- `FORBIDDEN` (403) - Insufficient permissions
- `NOT_FOUND` (404) - Resource not found
- `VALIDATION_ERROR` (400) - Invalid request data
- `CONFLICT` (409) - Resource already exists
- `TOO_MANY_REQUESTS` (429) - Rate limit exceeded
- `INTERNAL_ERROR` (500) - Server error

## 📊 Rate Limits

- **Authentication endpoints**: 10 requests per minute
- **General API endpoints**: 100 requests per minute
- **WebSocket connections**: 5 connections per user
- **Image uploads**: 20 uploads per hour

## 🔧 SDK Examples

### JavaScript/TypeScript
```javascript
const HopieAPI = {
  baseURL: 'https://api.hopie-app.com',
  token: null,

  setToken(token) {
    this.token = token;
  },

  async request(endpoint, options = {}) {
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.token}`,
        ...options.headers
      }
    });
    return response.json();
  },

  async waterTree(message) {
    return this.request('/trees/water', {
      method: 'POST',
      body: JSON.stringify({ message })
    });
  }
};
```
